<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.miner.system.mapper.BarMapper">

    <resultMap type="com.miner.system.domain.Bar" id="BarResult">
        <result property="instId" column="inst_id"/>
        <result property="date" column="date"/>
        <result property="open" column="open"/>
        <result property="high" column="high"/>
        <result property="low" column="low"/>
        <result property="close" column="close"/>
        <result property="vol" column="vol"/>
        <result property="ts" column="ts"/>
        <result property="type" column="type"/>
    </resultMap>

    <insert id="insertIgnoreBatch" parameterType="java.util.List">
        INSERT IGNORE INTO bar (inst_id, date, open, high, low, close, vol, ts, type)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.instId}, #{item.date}, #{item.open}, #{item.high}, #{item.low}, #{item.close}, #{item.vol}, #{item.ts}, #{item.type})
        </foreach>
    </insert>

</mapper>
