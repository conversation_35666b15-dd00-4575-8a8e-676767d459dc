package com.miner.system.okx.service.marketData;

import com.alibaba.fastjson.JSONObject;

public interface MarketDataAPIService {

    //获取所有产品行情信息 Get Tickers
//    instType	String	产品类型
//    instId	String	产品ID
//    last	String	最新成交价
//    lastSz	String	最新成交的数量
//    askPx	String	卖一价
//    askSz	String	卖一价的挂单数数量
//    bidPx	String	买一价
//    bidSz	String	买一价的挂单数量
//    open24h	String	24小时开盘价
//    high24h	String	24小时最高价
//    low24h	String	24小时最低价
//    volCcy24h	String	24小时成交量，以币为单位
//    如果是衍生品合约，数值为交易货币的数量。
//    如果是币币/币币杠杆，数值为计价货币的数量。
//    vol24h	String	24小时成交量，以张为单位
//    如果是衍生品合约，数值为合约的张数。
//    如果是币币/币币杠杆，数值为交易货币的数量。
//    sodUtc0	String	UTC 0 时开盘价
//    sodUtc8	String	UTC+8 时开盘价
//    ts	String	ticker数据产生时间，Unix时间戳的毫秒数格式，如 159702638308
    JSONObject getTickers(String instType, String uly);

    //获取单个产品行情信息 Get Ticker
    JSONObject getTicker(String instId);

    //获取指数行情数据 Get Index Tickers
    JSONObject getIndexTickers(String quoteCcy, String instId);

    //获取产品深度 Get Order Book
    JSONObject getOrderBook(String instId, String sz);

    //获取所有交易产品K线数据 Get Candlesticks
    //    ts	String	开始时间，Unix时间戳的毫秒数格式，如 1597026383085
    //o	String	开盘价格
    //h	String	最高价格
    //l	String	最低价格
    //c	String	收盘价格
    //vol	String	交易量，以张为单位
    //如果是衍生品合约，数值为合约的张数。
    //如果是币币/币币杠杆，数值为交易货币的数量。
    //volCcy	String	交易量，以币为单位
    //如果是衍生品合约，数值为交易货币的数量。
    //如果是币币/币币杠杆，数值为计价货币的数量。
    //volCcyQuote	String	交易量，以计价货币为单位
    //如 BTC-USDT和BTC-USDT-SWAP，单位均是USDT。
    //BTC-USD-SWAP单位是USD。
    //confirm	String	K线状态
    //0：K线未完结
    //1：K线已完结
    JSONObject getCandlesticks(String instId, String after, String before, String bar, String limit);

    //获取交易产品历史K线数据（仅主流币） Get Candlesticks History（top currencies only）
    JSONObject getCandlesticksHistory(String instId, String after, String before, String bar, String limit);

    //获取指数K线数据 Get Index Candlesticks
    JSONObject getIndexCandlesticks(String instId, String after, String before, String bar, String limit);

    //获取历史指数K线数据 Get Index Candlesticks
    JSONObject getHistoryIndexCandlesticks(String instId, String after, String before, String bar, String limit);

    //获取标记价格K线数据 Get Mark Price Candlesticks
    //    返回参数
    //参数名	类型	描述
    //ts	String	开始时间，Unix时间戳的毫秒数格式，如 1597026383085
    //o	String	开盘价格
    //h	String	最高价格
    //l	String	最低价格
    //c	String	收盘价格
    //confirm	String	K线状态
    //0 代表 K 线未完结，1 代表 K 线已完结。
    JSONObject getMarkPriceCandlesticks(String instId, String after, String before, String bar, String limit);

    //获取交易产品公共成交数据 Get Trades
    JSONObject getTrades(String instId, String limit);

    //获取平台24小时总成交量 Get total volume
    JSONObject getTotalVolume();

}
