package com.miner.system.indicator;


import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

import lombok.Data;


/**
 * K线实体
 * Created by tifezh on 2016/5/16.
 */
@Data
public class KLineEntity implements IKLine, Serializable {

    String instId;
    public String Date;
    public BigDecimal Open;
    public BigDecimal High;
    public BigDecimal Low;
    public BigDecimal Close;
    public float Volume;
    public long ts;
    public String bar;

    public float MA5Price;

    public float MA10Price;

    public float MA20Price;

    public float MA30Price;

    public float MA60Price;

    public float MA55Price;

    public float MA75Price;

    public String buyText;

    public float dea;

    public float dif;

    public float macd;

    public float macdSignal;

    public float macdHist;

    public float k;

    public float d;

    public float j;

    public float r;

    public float rsi;

    public float stochK;

    public float stochD;

    //boll 上轨
    public float up;

    //boll 中轨
    public float mb;

    //bool 下轨
    public float dn;

    public float MA5Volume;

    public float MA10Volume;

    /**
     * 涨跌幅
     */
    public float percent;

    /**
     * 振幅
     */
    public float amplitude;

    public float pushPrice;

    public boolean isPush;

    public boolean isShort;
    public boolean isLong;

    public boolean isCloseShort;
    public boolean isCloseLong;

    public float pdi;

    public float mdi;


    private double ema5;
    private double ema10;
    private double ema15;

    @Override
    public String getDate() {
        return Date;
    }

    @Override
    public BigDecimal getOpenPrice() {
        return Open;
    }

    @Override
    public BigDecimal getHighPrice() {
        return High;
    }

    @Override
    public BigDecimal getLowPrice() {
        return Low;
    }

    @Override
    public BigDecimal getClosePrice() {
        return Close;
    }

    @Override
    public double getRate() {
        return Close.subtract(Open).divide(Open, 2, RoundingMode.HALF_UP).doubleValue() * 100;
    }

    @Override
    public float getMA5Price() {
        return MA5Price;
    }

    @Override
    public float getMA10Price() {
        return MA10Price;
    }

    @Override
    public float getMA20Price() {
        return MA20Price;
    }

    @Override
    public float getMA30Price() {
        return MA30Price;
    }

    @Override
    public float getMA60Price() {
        return MA60Price;
    }

    @Override
    public float getMA55Price() {
        return MA55Price;
    }

    @Override
    public float getMA75Price() {
        return MA75Price;
    }

    @Override
    public String getBuyText() {
        return buyText;
    }

    @Override
    public float getDea() {
        return dea;
    }

    @Override
    public float getDif() {
        return dif;
    }

    @Override
    public float getMacd() {
        return macd;
    }

    /**
     * 获取MACD信号线
     */
    public float getMacdSignal() {
        return macdSignal;
    }

    /**
     * 设置MACD信号线
     */
    public void setMacdSignal(float macdSignal) {
        this.macdSignal = macdSignal;
    }

    /**
     * 获取MACD柱状图值
     */
    public float getMacdHist() {
        return macdHist;
    }

    /**
     * 设置MACD柱状图值
     */
    public void setMacdHist(float macdHist) {
        this.macdHist = macdHist;
    }

    /**
     * 获取随机指标K值
     */
    public float getStochK() {
        return stochK;
    }

    /**
     * 设置随机指标K值
     */
    public void setStochK(double stochK) {
        this.stochK = (float)stochK;
    }

    /**
     * 获取随机指标D值
     */
    public float getStochD() {
        return stochD;
    }

    /**
     * 设置随机指标D值
     */
    public void setStochD(double stochD) {
        this.stochD = (float)stochD;
    }

    @Override
    public float getK() {
        return k;
    }

    @Override
    public float getD() {
        return d;
    }

    @Override
    public float getJ() {
        return j;
    }

    @Override
    public float getR() {
        return r;
    }

    @Override
    public float getRsi() {
        return rsi;
    }

    @Override
    public float getUp() {
        return up;
    }

    @Override
    public float getMb() {
        return mb;
    }

    @Override
    public float getDn() {
        return dn;
    }

    @Override
    public float getPercent() {
        return percent;
    }

    @Override
    public float getAmplitude() {
        return amplitude;
    }

    @Override
    public float getPushPrice() {
        return pushPrice;
    }

    @Override
    public boolean isBuy() {
        return isLong;
    }

    @Override
    public boolean isSell() {
        return isShort;
    }

    @Override
    public boolean isPush() {
        return isPush;
    }

    @Override
    public float getVolume() {
        return Volume;
    }

    @Override
    public float getMA5Volume() {
        return MA5Volume;
    }

    @Override
    public float getMA10Volume() {
        return MA10Volume;
    }

    @Override
    public float getPdi() {
        return pdi;
    }

    @Override
    public float getMdi() {
        return mdi;
    }
}
