package com.miner.common.translation.impl;

import com.miner.common.annotation.TranslationType;
import com.miner.common.constant.TransConstant;
import com.miner.common.core.service.DictService;
import com.miner.common.translation.TranslationInterface;
import com.miner.common.utils.StringUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 字典翻译实现
 *
 * <AUTHOR> Li
 */
@Component
@AllArgsConstructor
@TranslationType(type = TransConstant.DICT_TYPE_TO_LABEL)
public class DictTypeTranslationImpl implements TranslationInterface<String> {

    private final DictService dictService;

    @Override
    public String translation(Object key, String other) {
        if (key instanceof String && StringUtils.isNotBlank(other)) {
            return dictService.getDictLabel(other, key.toString());
        }
        return null;
    }
}
