<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="miner-server" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
    <deployment type="dockerfile">
      <settings>
        <option name="imageTag" value="miner-server:0.0.1" />
        <option name="containerName" value="miner-server" />
        <option name="sourceFilePath" value="miner-admin/Dockerfile" />
      </settings>
    </deployment>
    <method v="2" />
  </configuration>
</component>