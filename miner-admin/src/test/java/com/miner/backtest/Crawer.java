package com.miner.backtest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.miner.system.domain.Bar;
import com.miner.system.mapper.BarMapper;
import com.miner.system.okx.service.marketData.MarketDataAPIService;
import com.miner.system.okx.service.marketData.impl.MarketDataAPIServiceImpl;
import com.miner.system.okx.service.publicData.PublicDataAPIService;
import com.miner.system.okx.service.publicData.impl.PublicDataAPIServiceImpl;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 */
@Data
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class Crawer {

    PublicDataAPIService publicDataAPIService = new PublicDataAPIServiceImpl();

    MarketDataAPIService marketDataAPIService = new MarketDataAPIServiceImpl();

    @Resource
    BarMapper barMapper;

    @Test
    public void craw() {
        List<String> instIds = getInstIds();
        instIds.forEach(instId -> {
            long start = DateUtil.beginOfYear(new Date()).toJdkDate().getTime();
            long end = new Date().getTime();
            long step = 60000 * 15 * 100;
            List<Bar> bars = new ArrayList<>();
            for (long i = start; i < end; i += step) {
                JSONObject barJson = marketDataAPIService.getCandlesticksHistory(instId, String.valueOf(i + step), String.valueOf(i), "15m", "100");
                bars.addAll(parseBarList(barJson, instId));
                ThreadUtil.sleep(200);
            }
            log.info("{} 数据抓取成功 共{}条", instId, bars.size());
            barMapper.insertBatch(bars, 2000);
            log.info("{} 数据入库成功", instId);
        });
    }

    public List<String> getInstIds() {
        JSONObject instruments = publicDataAPIService.getInstruments("SWAP", null, null);
        List<String> ids = instruments.getJSONArray("data").stream().map(data -> {
            final JSONObject object = (JSONObject) data;
            final String instId = object.getString("instId");
            if (!instId.contains("USDT")) {
                return null;
            }
            return instId;
        }).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        return ids;
    }


    private List<Bar> parseBarList(JSONObject barJson, String instId) {
        List<Bar> barList = new ArrayList<>(100);
        JSONArray dataArray = barJson.getJSONArray("data");
        for (int i = 0; i < dataArray.size(); i++) {
            JSONArray temp = dataArray.getJSONArray(i);
            //    ts	String	开始时间，Unix时间戳的毫秒数格式，如 1597026383085
            //o	String	开盘价格
            //h	String	最高价格
            //l	String	最低价格
            //c	String	收盘价格
            long ts = temp.getLong(0);
            String open = temp.getString(1);
            String high = temp.getString(2);
            String low = temp.getString(3);
            String close = temp.getString(4);
            BigDecimal volCcy = temp.getBigDecimal(7);
            barList.add(new Bar(instId, new Date(ts), open, high, low, close, volCcy, ts, "15m"));
        }
        return barList;
    }
}
