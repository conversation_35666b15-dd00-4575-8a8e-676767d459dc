package com.miner.strategy.backtest.loader;

import com.miner.strategy.backtest.config.BacktestConfig;
import com.miner.system.indicator.KLineEntity;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TASK-001 历史数据加载器测试
 * 验证数据管理模块的功能
 */
@SpringBootTest
public class HistoricalDataLoaderTest {

    private BacktestConfig testConfig;

    @BeforeEach
    void setUp() {
        testConfig = BacktestConfig.builder()
            .symbols(Arrays.asList("BTC-USDT-SWAP", "ETH-USDT-SWAP"))
            .startDate(LocalDateTime.now().minusDays(100))
            .endDate(LocalDateTime.now())
            .mainTimeframe("4H")
            .subTimeframe("5m")
            .dataSource("database")
            .build();
    }

    @Test
    void testLoadHistoricalDataWithCache() {
        // 测试缓存功能
        Map<String, List<KLineEntity>> data1 = HistoricalDataLoader.loadHistoricalData(testConfig);
        Map<String, List<KLineEntity>> data2 = HistoricalDataLoader.loadHistoricalData(testConfig);

        // 验证数据一致性
        assertEquals(data1.size(), data2.size());

        // 验证缓存生效（第二次调用应该更快）
        long startTime = System.currentTimeMillis();
        HistoricalDataLoader.loadHistoricalData(testConfig);
        long cacheTime = System.currentTimeMillis() - startTime;

        startTime = System.currentTimeMillis();
        HistoricalDataLoader.loadHistoricalData(testConfig, true); // 强制刷新
        long refreshTime = System.currentTimeMillis() - startTime;

        assertTrue(cacheTime < refreshTime, "缓存应该比强制刷新更快");
    }

    @Test
    void testDataQualityValidation() {
        // 测试数据质量验证
        Map<String, List<KLineEntity>> data = HistoricalDataLoader.loadHistoricalData(testConfig);

        for (Map.Entry<String, List<KLineEntity>> entry : data.entrySet()) {
            String symbol = entry.getKey();
            List<KLineEntity> klines = entry.getValue();

            // 验证数据不为空
            assertFalse(klines.isEmpty(), symbol + " 数据不应为空");

            // 验证数据质量
            for (KLineEntity kline : klines) {
                assertNotNull(kline.getOpen(), "开盘价不应为null");
                assertNotNull(kline.getHigh(), "最高价不应为null");
                assertNotNull(kline.getLow(), "最低价不应为null");
                assertNotNull(kline.getClose(), "收盘价不应为null");
                assertTrue(kline.getTs() > 0, "时间戳应大于0");

                // 验证价格逻辑
                double open = kline.getOpen().doubleValue();
                double high = kline.getHigh().doubleValue();
                double low = kline.getLow().doubleValue();
                double close = kline.getClose().doubleValue();

                assertTrue(high >= Math.max(open, close), "最高价应大于等于开盘价和收盘价");
                assertTrue(low <= Math.min(open, close), "最低价应小于等于开盘价和收盘价");
                assertTrue(high >= low, "最高价应大于等于最低价");
            }

            // 验证时间排序（降序）
            for (int i = 1; i < klines.size(); i++) {
                assertTrue(klines.get(i-1).getTs() >= klines.get(i).getTs(),
                    "数据应按时间降序排列");
            }
        }
    }

    @Test
    void testDataQualityReport() {
        // 加载数据以生成质量报告
        HistoricalDataLoader.loadHistoricalData(testConfig);

        // 获取质量报告
        Map<String, HistoricalDataLoader.DataQualityReport> reports =
            HistoricalDataLoader.getAllQualityReports();

        assertFalse(reports.isEmpty(), "应该生成数据质量报告");

        for (HistoricalDataLoader.DataQualityReport report : reports.values()) {
            assertNotNull(report.getSymbol(), "交易对不应为null");
            assertNotNull(report.getTimeframe(), "时间框架不应为null");
            assertTrue(report.getTotalRecords() >= 0, "总记录数应大于等于0");
            assertTrue(report.getValidRecords() >= 0, "有效记录数应大于等于0");
            assertTrue(report.getCompletenessRate() >= 0 && report.getCompletenessRate() <= 1,
                "完整性比率应在0-1之间");

            System.out.println("数据质量报告: " + report);
        }
    }

    @Test
    void testCacheManagement() {
        // 测试缓存管理
        HistoricalDataLoader.loadHistoricalData(testConfig);

        // 清理过期缓存
        HistoricalDataLoader.cleanExpiredCache();

        // 验证缓存仍然有效（因为刚刚创建）
        Map<String, List<KLineEntity>> cachedData = HistoricalDataLoader.loadHistoricalData(testConfig);
        assertFalse(cachedData.isEmpty(), "缓存数据应该仍然有效");
    }

    @Test
    void testIncrementalUpdate() {
        // 测试增量更新
        Map<String, List<KLineEntity>> initialData = HistoricalDataLoader.loadHistoricalData(testConfig);

        // 创建新的配置，时间范围稍有不同
        BacktestConfig incrementalConfig = BacktestConfig.builder()
            .symbols(testConfig.getSymbols())
            .startDate(LocalDateTime.now().minusDays(35))
            .endDate(LocalDateTime.now())
            .mainTimeframe(testConfig.getMainTimeframe())
            .subTimeframe(testConfig.getSubTimeframe())
            .dataSource(testConfig.getDataSource())
            .build();

        // 执行增量更新
        Map<String, List<KLineEntity>> updatedData =
            HistoricalDataLoader.updateIncrementalData(incrementalConfig, initialData);

        // 验证更新后的数据
        for (String symbol : testConfig.getSymbols()) {
            if (initialData.containsKey(symbol) && updatedData.containsKey(symbol)) {
                assertTrue(updatedData.get(symbol).size() >= initialData.get(symbol).size(),
                    "增量更新后数据量应该不减少");
            }
        }
    }

    @Test
    void testMockDataGeneration() {
        // 测试模拟数据生成
        BacktestConfig mockConfig = BacktestConfig.builder()
            .symbols(Arrays.asList("BTC-USDT-SWAP", "ETH-USDT-SWAP"))
            .startDate(LocalDateTime.now().minusDays(10))
            .endDate(LocalDateTime.now())
            .mainTimeframe("4H")
            .subTimeframe("5m")
            .build();

        Map<String, List<KLineEntity>> mockData = HistoricalDataLoader.createMockData(mockConfig);

        assertFalse(mockData.isEmpty(), "应该生成模拟数据");

        for (Map.Entry<String, List<KLineEntity>> entry : mockData.entrySet()) {
            String symbol = entry.getKey();
            List<KLineEntity> klines = entry.getValue();

            assertFalse(klines.isEmpty(), symbol + " 模拟数据不应为空");

            // 验证模拟数据的基本属性
            for (KLineEntity kline : klines) {
                assertNotNull(kline.getInstId(), "交易对ID不应为null");
                assertTrue(kline.getOpen().doubleValue() > 0, "开盘价应大于0");
                assertTrue(kline.getHigh().doubleValue() > 0, "最高价应大于0");
                assertTrue(kline.getLow().doubleValue() > 0, "最低价应大于0");
                assertTrue(kline.getClose().doubleValue() > 0, "收盘价应大于0");
                assertTrue(kline.getVolume() > 0, "成交量应大于0");
            }
        }
    }

    @Test
    void testDataSourceSwitching() {
        // 测试不同数据源
        String[] dataSources = {"database", "csv", "exchange"};

        for (String dataSource : dataSources) {
            BacktestConfig config = BacktestConfig.builder()
                .symbols(Arrays.asList("BTC-USDT-SWAP"))
                .startDate(LocalDateTime.now().minusDays(5))
                .endDate(LocalDateTime.now())
                .mainTimeframe("4H")
                .subTimeframe("5m")
                .dataSource(dataSource)
                .csvFilePath("test.csv") // 仅用于CSV测试
                .build();

            try {
                Map<String, List<KLineEntity>> data = HistoricalDataLoader.loadHistoricalData(config);
                // 不同数据源可能返回不同结果，这里只验证不抛异常
                assertNotNull(data, dataSource + " 数据源应该返回非null结果");
            } catch (Exception e) {
                // 某些数据源可能不可用，记录但不失败测试
                System.out.println("数据源 " + dataSource + " 不可用: " + e.getMessage());
            }
        }
    }

    @Test
    void testPerformanceRequirements() {
        // 测试性能要求 - 数据加载时间应小于30秒
        long startTime = System.currentTimeMillis();

        BacktestConfig performanceConfig = BacktestConfig.builder()
            .symbols(Arrays.asList("BTC-USDT-SWAP", "ETH-USDT-SWAP"))
            .startDate(LocalDateTime.now().minusDays(30)) // 30天数据
            .endDate(LocalDateTime.now())
            .mainTimeframe("4H")
            .subTimeframe("5m")
            .dataSource("database")
            .build();

        Map<String, List<KLineEntity>> data = HistoricalDataLoader.loadHistoricalData(performanceConfig);

        long loadTime = System.currentTimeMillis() - startTime;

        assertTrue(loadTime < 30000, "数据加载时间应小于30秒，实际: " + loadTime + "ms");
        assertTrue(data.size() > 0, "应该加载到数据");

        // 验证数据完整性 > 99%
        for (Map.Entry<String, List<KLineEntity>> entry : data.entrySet()) {
            String symbol = entry.getKey();
            HistoricalDataLoader.DataQualityReport report =
                HistoricalDataLoader.getQualityReport(symbol, performanceConfig.getMainTimeframe());

            if (report != null) {
                assertTrue(report.getCompletenessRate() > 0.99,
                    symbol + " 数据完整性应大于99%，实际: " + (report.getCompletenessRate() * 100) + "%");
            }
        }
    }
}
