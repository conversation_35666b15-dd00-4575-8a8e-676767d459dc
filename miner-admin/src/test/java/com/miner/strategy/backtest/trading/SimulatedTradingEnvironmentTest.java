package com.miner.strategy.backtest.trading;

import com.miner.strategy.backtest.core.event.EventBus;
import com.miner.strategy.backtest.core.event.SignalEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 模拟交易环境测试类
 *
 * <AUTHOR>
 */
class SimulatedTradingEnvironmentTest {

    private SimulatedTradingEnvironment tradingEnvironment;
    private EventBus eventBus;

    @BeforeEach
    void setUp() {
        eventBus = new EventBus();
        tradingEnvironment = new SimulatedTradingEnvironment(BigDecimal.valueOf(10000), eventBus);
    }

    @Test
    @DisplayName("测试账户初始化")
    void testAccountInitialization() {
        Account account = tradingEnvironment.getAccount();

        assertNotNull(account);
        assertEquals(BigDecimal.valueOf(10000), account.getInitialBalance());
        assertEquals(BigDecimal.valueOf(10000), account.getAvailableBalance());
        assertEquals(BigDecimal.ZERO, account.getUsedMargin());
        assertEquals(BigDecimal.ZERO, account.getUnrealizedPnl());
        assertEquals(BigDecimal.ZERO, account.getRealizedPnl());
    }

    @Test
    @DisplayName("测试买入信号处理")
    void testBuySignalProcessing() {
        // 设置市场价格
        tradingEnvironment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(50000));

        // 创建买入信号
        SignalEvent buySignal = new SignalEvent(
                "BTCUSDT",
                SignalEvent.SignalType.BUY,
                BigDecimal.valueOf(50000),
                BigDecimal.valueOf(0.1),
                "TestStrategy",
                0.8,
                System.currentTimeMillis()
        );

        // 处理信号
        eventBus.publish(buySignal);

        // 验证结果
        assertEquals(1, tradingEnvironment.getPositionManager().getActivePositionCount());

        Position position = tradingEnvironment.getPositionManager().getPosition("BTCUSDT");
        assertNotNull(position);
        assertEquals(Position.PositionSide.LONG, position.getSide());
        assertEquals(BigDecimal.valueOf(0.1), position.getQuantity());
        assertEquals(BigDecimal.valueOf(50000), position.getAvgEntryPrice());
    }

    @Test
    @DisplayName("测试卖出信号处理")
    void testSellSignalProcessing() {
        // 设置市场价格
        tradingEnvironment.updateMarketPrice("ETHUSDT", BigDecimal.valueOf(3000));

        // 创建卖出信号
        SignalEvent sellSignal = new SignalEvent(
                "ETHUSDT",
                SignalEvent.SignalType.SELL,
                BigDecimal.valueOf(3000),
                BigDecimal.valueOf(1.0),
                "TestStrategy",
                0.8,
                System.currentTimeMillis()
        );

        // 处理信号
        eventBus.publish(sellSignal);

        // 验证结果
        assertEquals(1, tradingEnvironment.getPositionManager().getActivePositionCount());

        Position position = tradingEnvironment.getPositionManager().getPosition("ETHUSDT");
        assertNotNull(position);
        assertEquals(Position.PositionSide.SHORT, position.getSide());
        assertEquals(BigDecimal.valueOf(1.0), position.getQuantity());
        assertEquals(BigDecimal.valueOf(3000), position.getAvgEntryPrice());
    }

    @Test
    @DisplayName("测试平仓信号处理")
    void testCloseSignalProcessing() {
        // 先开仓
        tradingEnvironment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(50000));
        SignalEvent buySignal = new SignalEvent(
                "BTCUSDT",
                SignalEvent.SignalType.BUY,
                BigDecimal.valueOf(50000),
                BigDecimal.valueOf(0.1),
                "TestStrategy",
                0.8,
                System.currentTimeMillis()
        );
        eventBus.publish(buySignal);

        // 验证开仓成功
        assertEquals(1, tradingEnvironment.getPositionManager().getActivePositionCount());

        // 更新价格并平仓
        tradingEnvironment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(55000));
        SignalEvent closeSignal = new SignalEvent(
                "BTCUSDT",
                SignalEvent.SignalType.CLOSE_LONG,
                BigDecimal.valueOf(55000),
                BigDecimal.valueOf(0.1),
                "TestStrategy",
                0.8,
                System.currentTimeMillis()
        );
        eventBus.publish(closeSignal);

        // 验证平仓成功
        assertEquals(0, tradingEnvironment.getPositionManager().getActivePositionCount());

        // 验证盈利
        Account account = tradingEnvironment.getAccount();
        assertTrue(account.getRealizedPnl().compareTo(BigDecimal.ZERO) > 0);
    }

    @Test
    @DisplayName("测试风险控制")
    void testRiskControl() {
        // 设置市场价格
        tradingEnvironment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(50000));

        // 尝试开一个超大仓位（应该被风控拒绝）
        SignalEvent largeSignal = new SignalEvent(
                "BTCUSDT",
                SignalEvent.SignalType.BUY,
                BigDecimal.valueOf(50000),
                BigDecimal.valueOf(10.0), // 超大数量
                "TestStrategy",
                0.8,
                System.currentTimeMillis()
        );

        // 处理信号
        eventBus.publish(largeSignal);

        // 验证风控生效（没有开仓）
        assertEquals(0, tradingEnvironment.getPositionManager().getActivePositionCount());

        // 验证拒绝统计
        assertTrue(tradingEnvironment.getTradingStats().rejectedSignals > 0);
    }

    @Test
    @DisplayName("测试账户保证金管理")
    void testMarginManagement() {
        Account account = tradingEnvironment.getAccount();

        // 初始状态
        assertEquals(BigDecimal.valueOf(10000), account.getAvailableBalance());
        assertEquals(BigDecimal.ZERO, account.getUsedMargin());

        // 开仓
        tradingEnvironment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(50000));
        SignalEvent buySignal = new SignalEvent(
                "BTCUSDT",
                SignalEvent.SignalType.BUY,
                BigDecimal.valueOf(50000),
                BigDecimal.valueOf(0.1),
                "TestStrategy",
                0.8,
                System.currentTimeMillis()
        );
        eventBus.publish(buySignal);

        // 验证保证金被冻结
        assertTrue(account.getUsedMargin().compareTo(BigDecimal.ZERO) > 0);
        assertTrue(account.getAvailableBalance().compareTo(BigDecimal.valueOf(10000)) < 0);

        // 验证总权益不变（忽略手续费）
        BigDecimal totalEquity = account.getAvailableBalance().add(account.getUsedMargin());
        assertTrue(totalEquity.compareTo(BigDecimal.valueOf(9990)) > 0); // 考虑手续费
    }

    @Test
    @DisplayName("测试价格更新和未实现盈亏")
    void testPriceUpdateAndUnrealizedPnl() {
        // 开仓
        tradingEnvironment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(50000));
        SignalEvent buySignal = new SignalEvent(
                "BTCUSDT",
                SignalEvent.SignalType.BUY,
                BigDecimal.valueOf(50000),
                BigDecimal.valueOf(0.1),
                "TestStrategy",
                0.8,
                System.currentTimeMillis()
        );
        eventBus.publish(buySignal);

        Account account = tradingEnvironment.getAccount();

        // 价格上涨，应该有未实现盈利
        tradingEnvironment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(55000));
        assertTrue(account.getUnrealizedPnl().compareTo(BigDecimal.ZERO) > 0);

        // 价格下跌，应该有未实现亏损
        tradingEnvironment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(45000));
        assertTrue(account.getUnrealizedPnl().compareTo(BigDecimal.ZERO) < 0);
    }

    @Test
    @DisplayName("测试订单执行引擎")
    void testOrderExecution() {
        // 设置市场价格
        tradingEnvironment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(50000));

        // 创建订单
        Order order = Order.builder()
                .accountId(tradingEnvironment.getAccount().getAccountId())
                .symbol("BTCUSDT")
                .orderType(Order.OrderType.MARKET)
                .side(Order.OrderSide.BUY)
                .quantity(BigDecimal.valueOf(0.01))
                .leverage(10)
                .createTime(LocalDateTime.now())
                .build();

        // 这里需要直接访问订单执行引擎进行测试
        // 由于封装性，我们通过信号来间接测试
        SignalEvent signal = new SignalEvent(
                "BTCUSDT",
                SignalEvent.SignalType.BUY,
                BigDecimal.valueOf(50000),
                BigDecimal.valueOf(0.1),
                "TestStrategy",
                0.8,
                System.currentTimeMillis()
        );

        eventBus.publish(signal);

        // 验证交易统计
        SimulatedTradingEnvironment.TradingStats stats = tradingEnvironment.getTradingStats();
        assertEquals(1, stats.totalSignals);
        assertTrue(stats.successfulTrades > 0);
    }

    @Test
    @DisplayName("测试交易统计")
    void testTradingStatistics() {
        SimulatedTradingEnvironment.TradingStats stats = tradingEnvironment.getTradingStats();

        // 初始状态
        assertEquals(0, stats.totalSignals);
        assertEquals(0, stats.successfulTrades);
        assertEquals(0, stats.failedTrades);

        // 处理一个成功的信号
        tradingEnvironment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(50000));
        SignalEvent signal = new SignalEvent(
                "BTCUSDT",
                SignalEvent.SignalType.BUY,
                BigDecimal.valueOf(50000),
                BigDecimal.valueOf(0.1),
                "TestStrategy",
                0.8,
                System.currentTimeMillis()
        );
        eventBus.publish(signal);

        // 验证统计更新
        assertEquals(1, stats.totalSignals);
        assertTrue(stats.successfulTrades > 0);
        assertTrue(stats.getSuccessRate() > 0);
    }

    @Test
    @DisplayName("测试环境摘要")
    void testEnvironmentSummary() {
        String summary = tradingEnvironment.getEnvironmentSummary();

        assertNotNull(summary);
        assertTrue(summary.contains("交易环境摘要"));
        assertTrue(summary.contains("账户"));
        assertTrue(summary.contains("风险"));
        assertTrue(summary.contains("交易统计"));
        assertTrue(summary.contains("活跃持仓"));
    }
}
