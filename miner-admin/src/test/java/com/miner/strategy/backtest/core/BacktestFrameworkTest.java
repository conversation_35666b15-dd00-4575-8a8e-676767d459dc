package com.miner.strategy.backtest.core;

import com.miner.strategy.backtest.config.BacktestConfig;
import com.miner.strategy.backtest.core.event.*;
import com.miner.strategy.backtest.core.impl.DefaultBacktestEngine;
import com.miner.strategy.backtest.core.impl.DefaultTimeSeriesProcessor;
import com.miner.strategy.backtest.core.impl.EMARetraceStrategyAdapter;
import com.miner.system.indicator.KLineEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 回测框架测试类
 * 测试TASK-002实现的核心功能
 *
 * <AUTHOR>
 */
class BacktestFrameworkTest {

    private BacktestConfig config;
    private Map<String, List<KLineEntity>> testData;

    @BeforeEach
    void setUp() {
        // 创建测试配置
        config = BacktestConfig.builder()
            .startDate(LocalDateTime.of(2024, 1, 1, 0, 0))
            .endDate(LocalDateTime.of(2024, 1, 31, 23, 59))
            .symbols(Arrays.asList("BTCUSDT", "ETHUSDT"))
            .initialCapital(10000.0)
            .positionSizeRatio(0.02)
            .mainTimeframe("4h")
            .subTimeframe("5m")
            .emaFast(9)
            .emaMid(21)
            .emaSlow(55)
            .rsiPeriod(14)
            .rsiOversold(30.0)
            .rsiRising(50.0)
            .entryScoreThreshold(7.0)
            .atrPeriod(14)
            .stopLossMultiplier(2.0)
            .takeProfitMultiplier1(10.0)
            .takeProfitMultiplier2(20.0)
            .tradeFeeRate(0.001)
            .slippageRatio(0.001)
            .build();

        // 创建测试数据
        testData = createTestData();
    }

    @Test
    @DisplayName("测试事件总线功能")
    void testEventBus() {
        EventBus eventBus = new EventBus();
        TestEventHandler handler = new TestEventHandler();

        // 注册处理器
        eventBus.register(handler);
        assertEquals(1, eventBus.getHandlerCount(Event.EventType.MARKET_DATA));

        // 发布事件
        KLineEntity kline = createTestKLine("BTCUSDT", 50000.0, System.currentTimeMillis());
        MarketDataEvent event = new MarketDataEvent("BTCUSDT", kline, "4h");
        eventBus.publish(event);

        // 验证处理器被调用
        assertTrue(handler.wasHandled());
        assertEquals("BTCUSDT", handler.getLastSymbol());

        // 取消注册
        eventBus.unregister(handler);
        assertEquals(0, eventBus.getHandlerCount(Event.EventType.MARKET_DATA));

        eventBus.shutdown();
    }

    @Test
    @DisplayName("测试策略工厂功能")
    void testStrategyFactory() {
        StrategyFactory factory = StrategyFactory.getInstance();

        // 注册测试策略
        factory.registerStrategy("TestStrategy", TestStrategy::new, "测试策略", "1.0.0");

        assertTrue(factory.isStrategyRegistered("TestStrategy"));
        assertEquals(1, factory.getRegisteredStrategyCount());

        // 创建策略实例
        TradingStrategy strategy = factory.createStrategy("TestStrategy", config);
        assertNotNull(strategy);
        assertEquals("TestStrategy", strategy.getStrategyName());
        assertEquals(TradingStrategy.StrategyState.ACTIVE, strategy.getState());

        // 清理
        factory.unregisterStrategy("TestStrategy");
        assertFalse(factory.isStrategyRegistered("TestStrategy"));
    }

    @Test
    @DisplayName("测试时间序列处理器")
    void testTimeSeriesProcessor() {
        TimeSeriesProcessor processor = new DefaultTimeSeriesProcessor();
        processor.initialize(testData);

        // 测试获取所有时间戳
        List<Long> timestamps = processor.getAllTimestamps();
        assertFalse(timestamps.isEmpty());

        // 验证时间戳是排序的
        for (int i = 1; i < timestamps.size(); i++) {
            assertTrue(timestamps.get(i) >= timestamps.get(i - 1));
        }

        // 测试获取K线数据
        long timestamp = timestamps.get(0);
        KLineEntity kline = processor.getKLineAt("BTCUSDT", timestamp, "4h");
        assertNotNull(kline);

        // 测试数据完整性检查
        TimeSeriesProcessor.DataIntegrityResult integrity =
            processor.checkDataIntegrity("BTCUSDT", "4h");
        assertNotNull(integrity);
        assertTrue(integrity.getCompleteness() > 0);
    }

    @Test
    @DisplayName("测试策略执行器")
    void testStrategyExecutor() {
        EventBus eventBus = new EventBus();
        StrategyExecutor executor = new StrategyExecutor(eventBus);

        // 创建并注册测试策略
        TestStrategy strategy = new TestStrategy();
        strategy.initialize(config);
        executor.registerStrategy(strategy);

        assertEquals(1, executor.getStrategyCount());
        assertEquals(1, executor.getActiveStrategyCount());

        // 创建市场数据事件
        KLineEntity kline = createTestKLine("BTCUSDT", 50000.0, System.currentTimeMillis());
        MarketDataEvent event = new MarketDataEvent("BTCUSDT", kline, "4h");

        // 执行策略
        executor.executeStrategies(event);

        // 验证策略被调用
        assertTrue(strategy.wasProcessed());

        executor.shutdown();
        eventBus.shutdown();
    }

    @Test
    @DisplayName("测试EMA策略适配器")
    void testEMAStrategyAdapter() {
        EMARetraceStrategyAdapter strategy = new EMARetraceStrategyAdapter();
        strategy.initialize(config);

        assertEquals("EMARetraceStrategy", strategy.getStrategyName());
        assertEquals("2.0.0", strategy.getVersion());
        assertEquals(TradingStrategy.StrategyState.ACTIVE, strategy.getState());

        // 测试参数验证
        Map<String, Object> validParams = new HashMap<>();
        validParams.put("emaFast", 9);
        validParams.put("emaMid", 21);
        validParams.put("emaSlow", 55);
        validParams.put("rsiPeriod", 14);
        validParams.put("rsiOversold", 30.0);

        TradingStrategy.ParameterValidationResult validation =
            strategy.validateParameters(validParams);
        assertTrue(validation.isValid());

        // 测试无效参数
        Map<String, Object> invalidParams = new HashMap<>();
        invalidParams.put("emaFast", 21);  // 错误：emaFast >= emaMid
        invalidParams.put("emaMid", 21);
        invalidParams.put("emaSlow", 55);

        validation = strategy.validateParameters(invalidParams);
        assertFalse(validation.isValid());
        assertFalse(validation.getErrors().isEmpty());
    }

    @Test
    @DisplayName("测试回测引擎")
    void testBacktestEngine() {
        BacktestEngine engine = new DefaultBacktestEngine();

        // 测试数据验证
        BacktestEngine.DataValidationResult validation = engine.validateData(testData);
        assertTrue(validation.isValid());
        assertTrue(validation.getCompleteness() > 0.9);

        // 测试初始化
        assertDoesNotThrow(() -> engine.initialize(config, testData));
        assertEquals(BacktestEngine.BacktestState.INITIALIZED, engine.getState());

        // 测试状态控制
        engine.pause();
        assertEquals(BacktestEngine.BacktestState.INITIALIZED, engine.getState()); // 只有运行中才能暂停

        engine.stop();
        assertEquals(BacktestEngine.BacktestState.STOPPED, engine.getState());
    }

    /**
     * 创建测试数据
     */
    private Map<String, List<KLineEntity>> createTestData() {
        Map<String, List<KLineEntity>> data = new HashMap<>();

        // 为BTCUSDT创建测试数据
        List<KLineEntity> btcData = new ArrayList<>();
        long baseTime = System.currentTimeMillis() - 24 * 60 * 60 * 1000; // 24小时前

        for (int i = 0; i < 100; i++) {
            btcData.add(createTestKLine("BTCUSDT", 50000.0 + i * 100, baseTime + i * 4 * 60 * 60 * 1000));
        }

        data.put("BTCUSDT_4h", btcData);

        // 为ETHUSDT创建测试数据
        List<KLineEntity> ethData = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            ethData.add(createTestKLine("ETHUSDT", 3000.0 + i * 50, baseTime + i * 4 * 60 * 60 * 1000));
        }

        data.put("ETHUSDT_4h", ethData);

        return data;
    }

    /**
     * 创建测试K线数据
     */
    private KLineEntity createTestKLine(String symbol, double price, long timestamp) {
        KLineEntity kline = new KLineEntity();
        kline.setInstId(symbol);
        kline.setTs(timestamp);
        kline.setBar("4h");
        kline.setOpen(BigDecimal.valueOf(price * 0.99));
        kline.setHigh(BigDecimal.valueOf(price * 1.02));
        kline.setLow(BigDecimal.valueOf(price * 0.98));
        kline.setClose(BigDecimal.valueOf(price));
        kline.setVolume(BigDecimal.valueOf(1000).floatValue());
        return kline;
    }

    /**
     * 测试事件处理器
     */
    private static class TestEventHandler implements EventHandler<MarketDataEvent> {
        private boolean handled = false;
        private String lastSymbol;

        @Override
        public void handle(MarketDataEvent event) {
            handled = true;
            lastSymbol = event.getSymbol();
        }

        @Override
        public String getHandlerName() {
            return "TestEventHandler";
        }

        @Override
        public Event.EventType getSupportedEventType() {
            return Event.EventType.MARKET_DATA;
        }

        public boolean wasHandled() {
            return handled;
        }

        public String getLastSymbol() {
            return lastSymbol;
        }
    }

    /**
     * 测试策略
     */
    private static class TestStrategy implements TradingStrategy {
        private StrategyState state = StrategyState.INITIALIZED;
        private boolean processed = false;

        @Override
        public void initialize(BacktestConfig config) {
            state = StrategyState.ACTIVE;
        }

        @Override
        public List<SignalEvent> onMarketData(MarketDataEvent event) {
            processed = true;
            return Collections.emptyList();
        }

        @Override
        public String getStrategyName() {
            return "TestStrategy";
        }

        @Override
        public String getVersion() {
            return "1.0.0";
        }

        @Override
        public String getDescription() {
            return "测试策略";
        }

        @Override
        public Map<String, Object> getParameters() {
            return Collections.emptyMap();
        }

        @Override
        public void setParameters(Map<String, Object> parameters) {
        }

        @Override
        public ParameterValidationResult validateParameters(Map<String, Object> parameters) {
            return new ParameterValidationResult(true, "验证通过", Collections.emptyList());
        }

        @Override
        public void reset() {
            state = StrategyState.INITIALIZED;
            processed = false;
        }

        @Override
        public StrategyState getState() {
            return state;
        }

        public boolean wasProcessed() {
            return processed;
        }
    }
}
