package com.miner.strategy.backtest.strategy;

import com.miner.strategy.backtest.config.BacktestConfig;
import com.miner.strategy.backtest.core.StrategyFactory;
import com.miner.strategy.backtest.core.TradingStrategy;
import com.miner.strategy.backtest.core.event.EventBus;
import com.miner.strategy.backtest.core.event.MarketDataEvent;
import com.miner.strategy.backtest.core.event.SignalEvent;
import com.miner.strategy.backtest.core.impl.EMARetraceStrategyAdapter;
import com.miner.strategy.backtest.indicators.TechnicalIndicators;
import com.miner.system.indicator.KLineEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * EMA策略集成测试
 * 测试TASK-004实现的策略集成功能
 *
 * <AUTHOR>
 */
class EMAStrategyIntegrationTest {

    private BacktestConfig config;
    private EMARetraceStrategyAdapter strategy;
    private EventBus eventBus;

    @BeforeEach
    void setUp() {
        // 创建测试配置
        config = BacktestConfig.builder()
            .startDate(LocalDateTime.of(2024, 1, 1, 0, 0))
            .endDate(LocalDateTime.of(2024, 1, 31, 23, 59))
            .symbols(Arrays.asList("BTCUSDT", "ETHUSDT"))
            .initialCapital(10000.0)
            .positionSizeRatio(0.02)
            .mainTimeframe("4h")
            .subTimeframe("5m")
            .emaFast(9)
            .emaMid(21)
            .emaSlow(55)
            .rsiPeriod(14)
            .rsiOversold(30.0)
            .rsiRising(50.0)
            .entryScoreThreshold(7.0)
            .atrPeriod(14)
            .stopLossMultiplier(2.0)
            .takeProfitMultiplier1(10.0)
            .takeProfitMultiplier2(20.0)
            .tradeFeeRate(0.001)
            .slippageRatio(0.001)
            .build();

        // 创建策略实例
        strategy = new EMARetraceStrategyAdapter();
        strategy.initialize(config);

        // 创建事件总线
        eventBus = new EventBus();
    }

    @Test
    @DisplayName("测试策略工厂创建")
    void testStrategyFactoryCreation() {
        StrategyFactory factory = StrategyFactory.getInstance();

        // 注册策略
        factory.registerStrategy("EMARetraceStrategy", EMARetraceStrategyAdapter::new,
            "测试策略", "2.1.0");

        // 创建策略实例
        TradingStrategy createdStrategy = factory.createStrategy("EMARetraceStrategy", config);

        assertNotNull(createdStrategy);
        assertEquals("EMARetraceStrategy", createdStrategy.getStrategyName());
        assertEquals("2.1.0", createdStrategy.getVersion());
        assertEquals(TradingStrategy.StrategyState.ACTIVE, createdStrategy.getState());
    }

    @Test
    @DisplayName("测试策略参数配置")
    void testStrategyParameterConfiguration() {
        // 获取初始参数
        Map<String, Object> initialParams = strategy.getParameters();
        assertNotNull(initialParams);
        assertEquals(9, initialParams.get("emaFast"));
        assertEquals(21, initialParams.get("emaMid"));
        assertEquals(55, initialParams.get("emaSlow"));

        // 更新参数
        Map<String, Object> newParams = new HashMap<>();
        newParams.put("emaFast", 12);
        newParams.put("emaMid", 26);
        newParams.put("entryScoreThreshold", 8.0);

        strategy.setParameters(newParams);

        // 验证参数更新
        Map<String, Object> updatedParams = strategy.getParameters();
        assertEquals(12, updatedParams.get("emaFast"));
        assertEquals(26, updatedParams.get("emaMid"));
        assertEquals(8.0, updatedParams.get("entryScoreThreshold"));
    }

    @Test
    @DisplayName("测试策略参数验证")
    void testStrategyParameterValidation() {
        // 测试有效参数
        Map<String, Object> validParams = new HashMap<>();
        validParams.put("emaFast", 9);
        validParams.put("emaMid", 21);
        validParams.put("emaSlow", 55);
        validParams.put("rsiPeriod", 14);
        validParams.put("rsiOversold", 30.0);

        TradingStrategy.ParameterValidationResult result = strategy.validateParameters(validParams);
        assertTrue(result.isValid());

        // 测试无效参数（EMA顺序错误）
        Map<String, Object> invalidParams = new HashMap<>();
        invalidParams.put("emaFast", 55);  // 错误：快线大于慢线
        invalidParams.put("emaMid", 21);
        invalidParams.put("emaSlow", 9);

        result = strategy.validateParameters(invalidParams);
        assertFalse(result.isValid());
        assertFalse(result.getErrors().isEmpty());
    }

    @Test
    @DisplayName("测试技术指标计算")
    void testTechnicalIndicatorCalculation() {
        // 创建测试价格数据
        List<BigDecimal> prices = createTestPrices(100);

        // 测试EMA计算
        double ema9 = TechnicalIndicators.calculateEMA(prices, 9);
        double ema21 = TechnicalIndicators.calculateEMA(prices, 21);
        double ema55 = TechnicalIndicators.calculateEMA(prices, 55);

        assertTrue(ema9 > 0);
        assertTrue(ema21 > 0);
        assertTrue(ema55 > 0);

        // 测试RSI计算
        double rsi = TechnicalIndicators.calculateRSI(prices, 14);
        assertTrue(rsi >= 0 && rsi <= 100);

        // 测试EMA多头排列检查
        boolean bullishAlignment = TechnicalIndicators.isEMABullishAlignment(prices, 9, 21, 55);
        // 结果取决于测试数据，这里只验证方法能正常执行
        assertNotNull(bullishAlignment);
    }

    @Test
    @DisplayName("测试信号生成逻辑")
    void testSignalGeneration() {
        // 准备足够的历史数据
        feedHistoricalData(strategy, "BTCUSDT", 100);

        // 创建市场数据事件
        KLineEntity kline = createTestKLine("BTCUSDT", 50000.0, System.currentTimeMillis());
        MarketDataEvent event = new MarketDataEvent("BTCUSDT", kline, "4h");

        // 处理市场数据
        List<SignalEvent> signals = strategy.onMarketData(event);

        // 验证信号生成（可能为空，取决于条件）
        assertNotNull(signals);

        // 如果生成了信号，验证信号内容
        if (!signals.isEmpty()) {
            SignalEvent signal = signals.get(0);
            assertEquals("BTCUSDT", signal.getSymbol());
            assertEquals("EMARetraceStrategy", signal.getStrategyName());
            assertNotNull(signal.getSignalType());
            assertTrue(signal.getConfidence() >= 0 && signal.getConfidence() <= 1);
        }
    }

    @Test
    @DisplayName("测试策略配置管理器")
    void testStrategyConfigManager() {
        StrategyConfigManager configManager = StrategyConfigManager.getInstance();

        // 从回测配置创建策略配置
        StrategyConfigManager.StrategyConfig strategyConfig =
            configManager.createFromBacktestConfig(config);

        assertNotNull(strategyConfig);
        assertEquals("EMARetraceStrategy", strategyConfig.getStrategyName());
        assertTrue(strategyConfig.isEnabled());
        assertFalse(strategyConfig.getParameters().isEmpty());

        // 保存配置
        configManager.saveStrategyConfig("TestEMAStrategy", strategyConfig);

        // 获取配置
        StrategyConfigManager.StrategyConfig retrievedConfig =
            configManager.getStrategyConfig("TestEMAStrategy");

        assertNotNull(retrievedConfig);
        assertEquals("EMARetraceStrategy", retrievedConfig.getStrategyName());
    }

    @Test
    @DisplayName("测试策略监控器")
    void testStrategyMonitor() {
        StrategyMonitor monitor = StrategyMonitor.getInstance();

        // 注册策略监控
        monitor.registerStrategy(strategy);

        // 更新策略状态
        monitor.updateStrategyState("EMARetraceStrategy", TradingStrategy.StrategyState.ACTIVE);

        // 记录信号生成
        monitor.recordSignalGenerated("EMARetraceStrategy", "BUY", 0.8);

        // 获取监控指标
        StrategyMonitor.StrategyMetrics metrics = monitor.getStrategyMetrics("EMARetraceStrategy");

        assertNotNull(metrics);
        assertEquals("EMARetraceStrategy", metrics.getStrategyName());
        assertEquals(1, metrics.getTotalSignals());
        assertEquals(1, metrics.getTradingSignals());
        assertEquals(0.8, metrics.getAverageConfidence(), 0.01);
    }

    @Test
    @DisplayName("测试信号日志记录器")
    void testSignalLogger() {
        SignalLogger signalLogger = SignalLogger.getInstance();

        // 创建测试信号
        Map<String, Object> signalData = new HashMap<>();
        signalData.put("entryScore", 8.5);
        signalData.put("emaFast", 50100.0);
        signalData.put("emaMid", 49800.0);
        signalData.put("emaSlow", 49500.0);
        signalData.put("rsi", 25.0);

        SignalEvent signal = new SignalEvent(
            "BTCUSDT",
            SignalEvent.SignalType.BUY,
            BigDecimal.valueOf(50000),
            BigDecimal.valueOf(0.1),
            "EMARetraceStrategy",
            0.85,
            System.currentTimeMillis(),
            signalData
        );

        // 记录信号
        signalLogger.logSignal(signal);

        // 获取统计信息
        SignalLogger.SignalStatistics stats = signalLogger.getStrategyStatistics("EMARetraceStrategy");

        assertNotNull(stats);
        assertEquals("EMARetraceStrategy", stats.getStrategyName());
        assertEquals(1, stats.getTotalSignals());
        assertEquals(1, stats.getBuySignals());
        assertEquals(0.85, stats.getAverageConfidence(), 0.01);
        assertEquals(8.5, stats.getAverageEntryScore(), 0.01);
    }

    @Test
    @DisplayName("测试策略状态管理")
    void testStrategyStateManagement() {
        // 初始状态
        assertEquals(TradingStrategy.StrategyState.ACTIVE, strategy.getState());

        // 重置策略
        strategy.reset();
        assertEquals(TradingStrategy.StrategyState.INITIALIZED, strategy.getState());

        // 重新初始化
        strategy.initialize(config);
        assertEquals(TradingStrategy.StrategyState.ACTIVE, strategy.getState());
    }

    /**
     * 创建测试价格数据
     */
    private List<BigDecimal> createTestPrices(int count) {
        List<BigDecimal> prices = new ArrayList<>();
        double basePrice = 50000.0;

        for (int i = 0; i < count; i++) {
            // 模拟价格波动
            double price = basePrice + (Math.sin(i * 0.1) * 1000) + (Math.random() * 200 - 100);
            prices.add(BigDecimal.valueOf(price));
        }

        return prices;
    }

    /**
     * 创建测试K线数据
     */
    private KLineEntity createTestKLine(String symbol, double price, long timestamp) {
        KLineEntity kline = new KLineEntity();
        kline.setInstId(symbol);
        kline.setTs(timestamp);
        kline.setBar("4h");
        kline.setOpen(BigDecimal.valueOf(price * 0.99));
        kline.setHigh(BigDecimal.valueOf(price * 1.02));
        kline.setLow(BigDecimal.valueOf(price * 0.98));
        kline.setClose(BigDecimal.valueOf(price));
        kline.setVolume(BigDecimal.valueOf(1000).floatValue());
        return kline;
    }

    /**
     * 为策略提供历史数据
     */
    private void feedHistoricalData(TradingStrategy strategy, String symbol, int count) {
        long baseTime = System.currentTimeMillis() - count * 4 * 60 * 60 * 1000L; // 4小时间隔

        for (int i = 0; i < count; i++) {
            KLineEntity kline = createTestKLine(symbol, 50000.0 + i * 10, baseTime + i * 4 * 60 * 60 * 1000L);
            MarketDataEvent event = new MarketDataEvent(symbol, kline, "4h");
            strategy.onMarketData(event);
        }
    }
}
