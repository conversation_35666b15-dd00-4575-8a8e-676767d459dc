package com.miner.strategy.backtest.example;

import com.miner.strategy.backtest.core.event.EventBus;
import com.miner.strategy.backtest.core.event.SignalEvent;
import com.miner.strategy.backtest.trading.Position;
import com.miner.strategy.backtest.trading.SimulatedTradingEnvironment;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

/**
 * 模拟交易环境使用示例
 * 展示如何使用TASK-003实现的模拟交易环境
 *
 * <AUTHOR>
 */
@Slf4j
public class TradingEnvironmentExample {

    public static void main(String[] args) {
        try {
            // 演示基本交易流程
            demonstrateBasicTrading();

            // 演示风险控制
            demonstrateRiskControl();

            // 演示持仓管理
            demonstratePositionManagement();

        } catch (Exception e) {
            log.error("示例执行失败", e);
        }
    }

    /**
     * 演示基本交易流程
     */
    public static void demonstrateBasicTrading() {
        log.info("=== 基本交易流程演示 ===");

        // 1. 创建交易环境
        EventBus eventBus = new EventBus();
        SimulatedTradingEnvironment environment = new SimulatedTradingEnvironment(
            BigDecimal.valueOf(10000), eventBus);

        log.info("初始状态: {}", environment.getAccount().getSummary());

        // 2. 设置市场价格
        environment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(50000));
        environment.updateMarketPrice("ETHUSDT", BigDecimal.valueOf(3000));

        // 3. 发送买入信号
        SignalEvent buyBTC = new SignalEvent(
            "BTCUSDT",
            SignalEvent.SignalType.BUY,
            BigDecimal.valueOf(50000),
            BigDecimal.valueOf(0.01),
            "DemoStrategy",
            0.8,
            System.currentTimeMillis()
        );

        eventBus.publish(buyBTC);
        log.info("买入BTC后: {}", environment.getAccount().getSummary());

        // 4. 发送买入ETH信号
        SignalEvent buyETH = new SignalEvent(
            "ETHUSDT",
            SignalEvent.SignalType.BUY,
            BigDecimal.valueOf(3000),
            BigDecimal.valueOf(0.1),
            "DemoStrategy",
            0.8,
            System.currentTimeMillis()
        );

        eventBus.publish(buyETH);
        log.info("买入ETH后: {}", environment.getAccount().getSummary());

        // 5. 模拟价格变化
        environment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(55000)); // 上涨10%
        environment.updateMarketPrice("ETHUSDT", BigDecimal.valueOf(2700));  // 下跌10%

        log.info("价格变化后: {}", environment.getAccount().getSummary());

        // 6. 平仓BTC（盈利）
        SignalEvent closeBTC = new SignalEvent(
            "BTCUSDT",
            SignalEvent.SignalType.CLOSE_LONG,
            BigDecimal.valueOf(55000),
            BigDecimal.valueOf(0.1),
            "DemoStrategy",
            0.8,
            System.currentTimeMillis()
        );

        eventBus.publish(closeBTC);
        log.info("平仓BTC后: {}", environment.getAccount().getSummary());

        // 7. 平仓ETH（亏损）
        SignalEvent closeETH = new SignalEvent(
            "ETHUSDT",
            SignalEvent.SignalType.CLOSE_LONG,
            BigDecimal.valueOf(2700),
            BigDecimal.valueOf(1.0),
            "DemoStrategy",
            0.8,
            System.currentTimeMillis()
        );

        eventBus.publish(closeETH);
        log.info("平仓ETH后: {}", environment.getAccount().getSummary());

        // 8. 输出最终统计
        log.info("交易统计: {}", environment.getTradingStats().getSummary());
        log.info("==================");

        eventBus.shutdown();
    }

    /**
     * 演示风险控制
     */
    public static void demonstrateRiskControl() {
        log.info("=== 风险控制演示 ===");

        EventBus eventBus = new EventBus();
        SimulatedTradingEnvironment environment = new SimulatedTradingEnvironment(
            BigDecimal.valueOf(1000), eventBus); // 较小的初始资金

        environment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(50000));

        log.info("初始状态: {}", environment.getAccount().getSummary());

        // 尝试开一个超大仓位（应该被风控拒绝）
        SignalEvent largeOrder = new SignalEvent(
            "BTCUSDT",
            SignalEvent.SignalType.BUY,
            BigDecimal.valueOf(50000),
            BigDecimal.valueOf(1.0), // 需要5万USDT，但只有1000USDT
            "RiskTestStrategy",
            0.8,
            System.currentTimeMillis()
        );

        eventBus.publish(largeOrder);

        log.info("尝试超大订单后: {}", environment.getAccount().getSummary());
        log.info("风险摘要: {}", environment.getRiskManager().getRiskSummary());
        log.info("交易统计: {}", environment.getTradingStats().getSummary());

        // 尝试合理大小的订单
        SignalEvent reasonableOrder = new SignalEvent(
            "BTCUSDT",
            SignalEvent.SignalType.BUY,
            BigDecimal.valueOf(50000),
            BigDecimal.valueOf(0.001), // 合理大小
            "RiskTestStrategy",
            0.8,
            System.currentTimeMillis()
        );

        eventBus.publish(reasonableOrder);

        log.info("合理订单后: {}", environment.getAccount().getSummary());
        log.info("活跃持仓数: {}", environment.getPositionManager().getActivePositionCount());
        log.info("==================");

        eventBus.shutdown();
    }

    /**
     * 演示持仓管理
     */
    public static void demonstratePositionManagement() {
        log.info("=== 持仓管理演示 ===");

        EventBus eventBus = new EventBus();
        SimulatedTradingEnvironment environment = new SimulatedTradingEnvironment(
            BigDecimal.valueOf(10000), eventBus);

        environment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(50000));

        // 1. 开仓
        SignalEvent openPosition = new SignalEvent(
            "BTCUSDT",
            SignalEvent.SignalType.BUY,
            BigDecimal.valueOf(50000),
            BigDecimal.valueOf(0.1),
            "PositionTestStrategy",
            0.8,
            System.currentTimeMillis()
        );

        eventBus.publish(openPosition);

        Position position = environment.getPositionManager().getPosition("BTCUSDT");
        if (position != null) {
            log.info("开仓后持仓: {}", position.getSummary());

            // 2. 设置止损止盈
            environment.getPositionManager().setStopLoss(position.getPositionId(), BigDecimal.valueOf(45000));
            environment.getPositionManager().setTakeProfit(position.getPositionId(), BigDecimal.valueOf(60000));

            log.info("设置止损止盈: 止损=45000, 止盈=60000");

            // 3. 模拟价格变化
            log.info("模拟价格变化...");

            // 价格上涨
            environment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(52000));
            log.info("价格52000: {}", position.getSummary());

            environment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(58000));
            log.info("价格58000: {}", position.getSummary());

            // 价格触发止盈
            environment.updateMarketPrice("BTCUSDT", BigDecimal.valueOf(61000));
            log.info("价格61000 (触发止盈): {}", environment.getAccount().getSummary());

            // 检查持仓是否已平仓
            if (environment.getPositionManager().getActivePositionCount() == 0) {
                log.info("持仓已自动平仓（止盈触发）");
            }
        }

        log.info("最终账户状态: {}", environment.getAccount().getSummary());
        log.info("==================");

        eventBus.shutdown();
    }

    /**
     * 演示完整的交易场景
     */
    public static void demonstrateCompleteScenario() {
        log.info("=== 完整交易场景演示 ===");

        EventBus eventBus = new EventBus();
        SimulatedTradingEnvironment environment = new SimulatedTradingEnvironment(
            BigDecimal.valueOf(50000), eventBus);

        // 模拟一天的交易
        String[] symbols = {"BTCUSDT", "ETHUSDT", "ADAUSDT"};
        BigDecimal[] prices = {
            BigDecimal.valueOf(50000),
            BigDecimal.valueOf(3000),
            BigDecimal.valueOf(1.5)
        };

        // 设置初始价格
        for (int i = 0; i < symbols.length; i++) {
            environment.updateMarketPrice(symbols[i], prices[i]);
        }

        log.info("开始模拟交易...");
        log.info("初始状态: {}", environment.getAccount().getSummary());

        // 模拟多次交易
        for (int round = 1; round <= 5; round++) {
            log.info("--- 第{}轮交易 ---", round);

            // 随机选择交易对和操作
            for (int i = 0; i < symbols.length; i++) {
                String symbol = symbols[i];
                BigDecimal currentPrice = prices[i];

                // 模拟价格波动
                double priceChange = (Math.random() - 0.5) * 0.1; // ±5%波动
                BigDecimal newPrice = currentPrice.multiply(BigDecimal.valueOf(1 + priceChange));
                environment.updateMarketPrice(symbol, newPrice);
                prices[i] = newPrice;

                // 随机决定是否交易
                if (Math.random() > 0.5) {
                    SignalEvent.SignalType signalType = Math.random() > 0.5 ?
                        SignalEvent.SignalType.BUY : SignalEvent.SignalType.SELL;

                    SignalEvent signal = new SignalEvent(
                        symbol,
                        signalType,
                        newPrice,
                        BigDecimal.valueOf(Math.random() * 0.1 + 0.01), // 0.01-0.11
                        "RandomStrategy",
                        Math.random(),
                        System.currentTimeMillis()
                    );

                    eventBus.publish(signal);
                }
            }

            log.info("第{}轮后: {}", round, environment.getAccount().getSummary());
            log.info("活跃持仓: {}", environment.getPositionManager().getActivePositionCount());

            // 模拟时间间隔
            try {
                TimeUnit.MILLISECONDS.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        // 平仓所有持仓
        log.info("平仓所有持仓...");
        for (Position position : environment.getPositionManager().getActivePositions()) {
            SignalEvent closeSignal = new SignalEvent(
                position.getSymbol(),
                position.isLongPosition() ? SignalEvent.SignalType.CLOSE_LONG : SignalEvent.SignalType.CLOSE_SHORT,
                position.getCurrentPrice(),
                position.getQuantity(),
                "CloseAllStrategy",
                1.0,
                System.currentTimeMillis()
            );
            eventBus.publish(closeSignal);
        }

        log.info("最终结果: {}", environment.getEnvironmentSummary());
        log.info("==================");

        eventBus.shutdown();
    }
}
