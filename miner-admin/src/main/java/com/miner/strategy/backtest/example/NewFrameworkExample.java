package com.miner.strategy.backtest.example;

import com.miner.strategy.backtest.config.BacktestConfig;
import com.miner.strategy.backtest.core.BacktestEngine;
import com.miner.strategy.backtest.core.StrategyFactory;
import com.miner.strategy.backtest.core.TradingStrategy;
import com.miner.strategy.backtest.core.impl.DefaultBacktestEngine;
import com.miner.strategy.backtest.loader.HistoricalDataLoader;
import com.miner.strategy.backtest.model.BacktestResult;
import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 新回测框架使用示例
 * 展示如何使用TASK-002实现的新框架
 *
 * <AUTHOR>
 */
@Slf4j
public class NewFrameworkExample {

    public static void main(String[] args) {
        try {
            // 1. 创建回测配置
            BacktestConfig config = createBacktestConfig();

            // 2. 加载历史数据
            Map<String, List<KLineEntity>> historicalData = loadHistoricalData(config);

            // 3. 创建并初始化回测引擎
            BacktestEngine engine = new DefaultBacktestEngine();
            engine.initialize(config, historicalData);

            // 4. 验证数据质量
            BacktestEngine.DataValidationResult validation = engine.validateData(historicalData);
            log.info("数据验证结果: {}, 完整性: {:.2f}%",
                    validation.isValid() ? "通过" : "失败",
                    validation.getCompleteness() * 100);

            if (!validation.isValid()) {
                log.error("数据验证失败: {}", validation.getMessage());
                return;
            }

            // 5. 运行回测
            log.info("开始运行回测...");
            BacktestResult result = engine.run();

            // 6. 输出结果
            printBacktestResult(result);

        } catch (Exception e) {
            log.error("回测执行失败", e);
        }
    }

    /**
     * 创建回测配置
     */
    private static BacktestConfig createBacktestConfig() {
        return BacktestConfig.builder()
            .startDate(LocalDateTime.of(2024, 1, 1, 0, 0))
            .endDate(LocalDateTime.of(2024, 12, 31, 23, 59))
            .symbols(Arrays.asList("BTC-USDT-SWAP"))
            .initialCapital(10000.0)
            .positionSizeRatio(0.02)
            .mainTimeframe("4H")
            .subTimeframe("5m")
            .dataSource("database")
            // EMA参数
            .emaFast(9)
            .emaMid(21)
            .emaSlow(55)
            // RSI参数
            .rsiPeriod(14)
            .rsiOversold(30.0)
            .rsiRising(50.0)
            .entryScoreThreshold(7.0)
            // ATR参数
            .atrPeriod(14)
            .stopLossMultiplier(2.0)
            .takeProfitMultiplier1(10.0)
            .takeProfitMultiplier2(20.0)
            // 费用设置
            .tradeFeeRate(0.001)
            .slippageRatio(0.001)
            .build();
    }

    /**
     * 加载历史数据
     */
    private static Map<String, List<KLineEntity>> loadHistoricalData(BacktestConfig config) {
        log.info("加载历史数据...");

        // 使用现有的数据加载器
        Map<String, List<KLineEntity>> data = HistoricalDataLoader.loadFromExchange(config);

        if (data.isEmpty()) {
            log.warn("未能加载到历史数据，使用模拟数据");
            // 这里可以生成一些模拟数据用于测试
            data = generateMockData(config);
        }

        log.info("历史数据加载完成，共 {} 个数据集", data.size());
        return data;
    }

    /**
     * 生成模拟数据（用于测试）
     */
    private static Map<String, List<KLineEntity>> generateMockData(BacktestConfig config) {
        // 这里可以实现模拟数据生成逻辑
        // 暂时返回空Map
        return new HashMap<>();
    }

    /**
     * 输出回测结果
     */
    private static void printBacktestResult(BacktestResult result) {
        if (result == null) {
            log.error("回测结果为空");
            return;
        }

        log.info("=== 回测结果 ===");
        log.info("策略名称: {}", result.getStrategyName());
        log.info("回测期间: {} 至 {}", result.getStartDate(), result.getEndDate());
        log.info("交易对: {}", result.getSymbols());
        log.info("初始资金: {:.2f} USDT", result.getInitialCapital());
        log.info("最终资金: {:.2f} USDT", result.getFinalCapital());

        if (result.getTotalTrades() > 0) {
            log.info("总交易次数: {}", result.getTotalTrades());
            log.info("盈利交易: {}", result.getWinningTrades());
            log.info("亏损交易: {}", result.getLosingTrades());
            log.info("胜率: {:.2f}%", result.getWinRate() * 100);
            log.info("收益率: {:.2f}%", result.getReturnRate() * 100);
            log.info("最大回撤: {:.2f}%", result.getMaxDrawdown() * 100);
            log.info("夏普比率: {:.2f}", result.getSharpeRatio());
        } else {
            log.info("未产生任何交易");
        }

        log.info("===============");
    }

    /**
     * 演示策略工厂的使用
     */
    public static void demonstrateStrategyFactory() {
        log.info("=== 策略工厂演示 ===");

        StrategyFactory factory = StrategyFactory.getInstance();

        // 显示已注册的策略
        log.info("已注册的策略:");
        for (String strategyName : factory.getRegisteredStrategyNames()) {
            StrategyFactory.StrategyDescriptor descriptor = factory.getStrategyDescriptor(strategyName);
            log.info("- {} (版本: {}) - {}",
                    strategyName, descriptor.getVersion(), descriptor.getDescription());
        }

        // 创建策略实例
        try {
            BacktestConfig config = createBacktestConfig();
            TradingStrategy strategy = factory.createStrategy("EMARetraceStrategy", config);

            log.info("成功创建策略实例: {}", strategy.getStrategyName());
            log.info("策略状态: {}", strategy.getState());
            log.info("策略参数: {}", strategy.getParameters());

        } catch (Exception e) {
            log.error("创建策略实例失败", e);
        }

        log.info("==================");
    }
}
