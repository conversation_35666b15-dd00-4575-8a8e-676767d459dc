package com.miner.strategy.backtest.core.event;

/**
 * 事件处理器接口
 * 定义事件处理的标准接口
 * 
 * <AUTHOR>
 */
public interface EventHandler<T extends Event> {
    
    /**
     * 处理事件
     * 
     * @param event 事件
     */
    void handle(T event);
    
    /**
     * 获取处理器名称
     * 
     * @return 处理器名称
     */
    String getHandlerName();
    
    /**
     * 获取支持的事件类型
     * 
     * @return 事件类型
     */
    Event.EventType getSupportedEventType();
    
    /**
     * 检查是否可以处理指定事件
     * 
     * @param event 事件
     * @return 是否可以处理
     */
    default boolean canHandle(Event event) {
        return event.getEventType() == getSupportedEventType();
    }
}
