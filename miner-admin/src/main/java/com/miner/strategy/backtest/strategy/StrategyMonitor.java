package com.miner.strategy.backtest.strategy;

import com.miner.strategy.backtest.core.TradingStrategy;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 策略状态监控器
 * 实时监控策略的运行状态和性能指标
 * 
 * <AUTHOR>
 */
@Slf4j
public class StrategyMonitor {
    
    private static final StrategyMonitor INSTANCE = new StrategyMonitor();
    
    // 策略监控数据
    private final Map<String, StrategyMetrics> strategyMetrics = new ConcurrentHashMap<>();
    
    // 定时任务执行器
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    
    // 监控配置
    private boolean monitoringEnabled = true;
    private int reportIntervalSeconds = 60; // 报告间隔
    
    private StrategyMonitor() {
        startMonitoring();
    }
    
    public static StrategyMonitor getInstance() {
        return INSTANCE;
    }
    
    /**
     * 注册策略监控
     * 
     * @param strategy 交易策略
     */
    public void registerStrategy(TradingStrategy strategy) {
        String strategyName = strategy.getStrategyName();
        StrategyMetrics metrics = new StrategyMetrics(strategyName);
        metrics.setStrategyVersion(strategy.getVersion());
        metrics.setStrategyDescription(strategy.getDescription());
        metrics.setRegistrationTime(LocalDateTime.now());
        metrics.setCurrentState(strategy.getState());
        
        strategyMetrics.put(strategyName, metrics);
        log.info("注册策略监控: {} (版本: {})", strategyName, strategy.getVersion());
    }
    
    /**
     * 取消注册策略监控
     * 
     * @param strategyName 策略名称
     */
    public void unregisterStrategy(String strategyName) {
        StrategyMetrics removed = strategyMetrics.remove(strategyName);
        if (removed != null) {
            log.info("取消注册策略监控: {}", strategyName);
        }
    }
    
    /**
     * 更新策略状态
     * 
     * @param strategyName 策略名称
     * @param state 策略状态
     */
    public void updateStrategyState(String strategyName, TradingStrategy.StrategyState state) {
        StrategyMetrics metrics = strategyMetrics.get(strategyName);
        if (metrics != null) {
            metrics.setCurrentState(state);
            metrics.setLastUpdateTime(LocalDateTime.now());
            
            // 记录状态变化
            if (metrics.getCurrentState() != state) {
                log.info("策略状态变化: {} {} -> {}", strategyName, metrics.getCurrentState(), state);
                metrics.incrementStateChanges();
            }
        }
    }
    
    /**
     * 记录信号生成
     * 
     * @param strategyName 策略名称
     * @param signalType 信号类型
     * @param confidence 信号置信度
     */
    public void recordSignalGenerated(String strategyName, String signalType, double confidence) {
        StrategyMetrics metrics = strategyMetrics.get(strategyName);
        if (metrics != null) {
            metrics.incrementTotalSignals();
            metrics.addConfidenceScore(confidence);
            metrics.setLastSignalTime(LocalDateTime.now());
            
            if ("BUY".equals(signalType) || "SELL".equals(signalType)) {
                metrics.incrementTradingSignals();
            }
            
            log.debug("记录信号: {} {} 置信度: {:.2f}", strategyName, signalType, confidence);
        }
    }
    
    /**
     * 记录策略异常
     * 
     * @param strategyName 策略名称
     * @param exception 异常信息
     */
    public void recordStrategyException(String strategyName, Exception exception) {
        StrategyMetrics metrics = strategyMetrics.get(strategyName);
        if (metrics != null) {
            metrics.incrementExceptionCount();
            metrics.setLastException(exception.getMessage());
            metrics.setLastExceptionTime(LocalDateTime.now());
            
            log.warn("策略异常: {} - {}", strategyName, exception.getMessage());
        }
    }
    
    /**
     * 记录处理时间
     * 
     * @param strategyName 策略名称
     * @param processingTimeMs 处理时间（毫秒）
     */
    public void recordProcessingTime(String strategyName, long processingTimeMs) {
        StrategyMetrics metrics = strategyMetrics.get(strategyName);
        if (metrics != null) {
            metrics.addProcessingTime(processingTimeMs);
        }
    }
    
    /**
     * 获取策略指标
     * 
     * @param strategyName 策略名称
     * @return 策略指标
     */
    public StrategyMetrics getStrategyMetrics(String strategyName) {
        return strategyMetrics.get(strategyName);
    }
    
    /**
     * 获取所有策略指标
     * 
     * @return 所有策略指标
     */
    public Map<String, StrategyMetrics> getAllStrategyMetrics() {
        return new ConcurrentHashMap<>(strategyMetrics);
    }
    
    /**
     * 生成监控报告
     * 
     * @return 监控报告
     */
    public String generateMonitoringReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 策略监控报告 ===\n");
        report.append("生成时间: ").append(LocalDateTime.now()).append("\n");
        report.append("监控策略数量: ").append(strategyMetrics.size()).append("\n\n");
        
        for (StrategyMetrics metrics : strategyMetrics.values()) {
            report.append(generateStrategyReport(metrics)).append("\n");
        }
        
        return report.toString();
    }
    
    /**
     * 生成单个策略报告
     * 
     * @param metrics 策略指标
     * @return 策略报告
     */
    private String generateStrategyReport(StrategyMetrics metrics) {
        StringBuilder report = new StringBuilder();
        report.append("策略: ").append(metrics.getStrategyName()).append("\n");
        report.append("  版本: ").append(metrics.getStrategyVersion()).append("\n");
        report.append("  状态: ").append(metrics.getCurrentState()).append("\n");
        report.append("  运行时间: ").append(metrics.getUptime()).append(" 分钟\n");
        report.append("  总信号数: ").append(metrics.getTotalSignals()).append("\n");
        report.append("  交易信号数: ").append(metrics.getTradingSignals()).append("\n");
        report.append("  平均置信度: ").append(String.format("%.2f", metrics.getAverageConfidence())).append("\n");
        report.append("  平均处理时间: ").append(String.format("%.2f", metrics.getAverageProcessingTime())).append(" ms\n");
        report.append("  异常次数: ").append(metrics.getExceptionCount()).append("\n");
        
        if (metrics.getLastSignalTime() != null) {
            report.append("  最后信号时间: ").append(metrics.getLastSignalTime()).append("\n");
        }
        
        if (metrics.getLastException() != null) {
            report.append("  最后异常: ").append(metrics.getLastException()).append("\n");
        }
        
        return report.toString();
    }
    
    /**
     * 启动监控
     */
    private void startMonitoring() {
        if (!monitoringEnabled) {
            return;
        }
        
        // 定期生成监控报告
        scheduler.scheduleAtFixedRate(() -> {
            try {
                if (!strategyMetrics.isEmpty()) {
                    String report = generateMonitoringReport();
                    log.info("策略监控报告:\n{}", report);
                }
            } catch (Exception e) {
                log.error("生成监控报告失败", e);
            }
        }, reportIntervalSeconds, reportIntervalSeconds, TimeUnit.SECONDS);
        
        // 定期清理过期数据
        scheduler.scheduleAtFixedRate(() -> {
            try {
                cleanupExpiredData();
            } catch (Exception e) {
                log.error("清理过期监控数据失败", e);
            }
        }, 300, 300, TimeUnit.SECONDS); // 每5分钟清理一次
        
        log.info("策略监控器已启动，报告间隔: {} 秒", reportIntervalSeconds);
    }
    
    /**
     * 清理过期数据
     */
    private void cleanupExpiredData() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(24); // 保留24小时数据
        
        strategyMetrics.entrySet().removeIf(entry -> {
            StrategyMetrics metrics = entry.getValue();
            return metrics.getLastUpdateTime().isBefore(cutoffTime);
        });
    }
    
    /**
     * 停止监控
     */
    public void shutdown() {
        monitoringEnabled = false;
        scheduler.shutdown();
        log.info("策略监控器已关闭");
    }
    
    /**
     * 策略指标数据类
     */
    @Data
    public static class StrategyMetrics {
        private final String strategyName;
        private String strategyVersion;
        private String strategyDescription;
        private TradingStrategy.StrategyState currentState;
        private LocalDateTime registrationTime;
        private LocalDateTime lastUpdateTime;
        private LocalDateTime lastSignalTime;
        
        // 信号统计
        private long totalSignals = 0;
        private long tradingSignals = 0;
        private double totalConfidence = 0.0;
        
        // 性能统计
        private long totalProcessingTime = 0;
        private long processingCount = 0;
        
        // 异常统计
        private long exceptionCount = 0;
        private String lastException;
        private LocalDateTime lastExceptionTime;
        
        // 状态统计
        private long stateChanges = 0;
        
        public StrategyMetrics(String strategyName) {
            this.strategyName = strategyName;
            this.lastUpdateTime = LocalDateTime.now();
        }
        
        public void incrementTotalSignals() {
            totalSignals++;
        }
        
        public void incrementTradingSignals() {
            tradingSignals++;
        }
        
        public void addConfidenceScore(double confidence) {
            totalConfidence += confidence;
        }
        
        public void incrementExceptionCount() {
            exceptionCount++;
        }
        
        public void incrementStateChanges() {
            stateChanges++;
        }
        
        public void addProcessingTime(long timeMs) {
            totalProcessingTime += timeMs;
            processingCount++;
        }
        
        public double getAverageConfidence() {
            return totalSignals > 0 ? totalConfidence / totalSignals : 0.0;
        }
        
        public double getAverageProcessingTime() {
            return processingCount > 0 ? (double) totalProcessingTime / processingCount : 0.0;
        }
        
        public long getUptime() {
            if (registrationTime == null) {
                return 0;
            }
            return java.time.Duration.between(registrationTime, LocalDateTime.now()).toMinutes();
        }
    }
}
