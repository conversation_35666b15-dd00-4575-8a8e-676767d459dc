package com.miner.strategy.backtest.trading;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 持仓管理器
 * 负责持仓的创建、更新、平仓等操作
 * 
 * <AUTHOR>
 */
@Slf4j
public class PositionManager {
    
    // 持仓ID生成器
    private final AtomicLong positionIdGenerator = new AtomicLong(1);
    
    // 活跃持仓 (accountId_symbol -> Position)
    private final Map<String, Position> activePositions = new ConcurrentHashMap<>();
    
    // 历史持仓
    private final Map<String, Position> historicalPositions = new ConcurrentHashMap<>();
    
    // 账户管理器
    private final Account account;
    
    public PositionManager(Account account) {
        this.account = account;
    }
    
    /**
     * 开仓
     * 
     * @param symbol 交易对
     * @param side 持仓方向
     * @param quantity 数量
     * @param entryPrice 开仓价格
     * @param leverage 杠杆倍数
     * @return 持仓ID
     */
    public String openPosition(String symbol, Position.PositionSide side, BigDecimal quantity, 
                              BigDecimal entryPrice, int leverage) {
        
        String positionKey = getPositionKey(symbol);
        Position existingPosition = activePositions.get(positionKey);
        
        if (existingPosition != null) {
            // 如果已有持仓，检查方向是否一致
            if (existingPosition.getSide() == side) {
                // 同方向加仓
                return addToPosition(existingPosition, quantity, entryPrice, leverage);
            } else {
                // 反方向，先平仓再开仓
                closePosition(existingPosition.getPositionId(), entryPrice, BigDecimal.ZERO);
            }
        }
        
        // 计算所需保证金
        BigDecimal positionValue = quantity.multiply(entryPrice);
        BigDecimal requiredMargin = positionValue.divide(BigDecimal.valueOf(leverage), 8, BigDecimal.ROUND_UP);
        
        // 检查并冻结保证金
        if (!account.freezeMargin(symbol, requiredMargin)) {
            log.warn("开仓失败：保证金不足。需要: {}, 可用: {}", requiredMargin, account.getAvailableBalance());
            return null;
        }
        
        // 创建新持仓
        String positionId = "POS_" + positionIdGenerator.getAndIncrement();
        Position position = Position.builder()
                .positionId(positionId)
                .accountId(account.getAccountId())
                .symbol(symbol)
                .side(side)
                .quantity(quantity)
                .avgEntryPrice(entryPrice)
                .currentPrice(entryPrice)
                .leverage(leverage)
                .margin(requiredMargin)
                .unrealizedPnl(BigDecimal.ZERO)
                .realizedPnl(BigDecimal.ZERO)
                .status(Position.PositionStatus.OPEN)
                .openTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .totalFee(BigDecimal.ZERO)
                .highestPrice(entryPrice)
                .lowestPrice(entryPrice)
                .build();
        
        activePositions.put(positionKey, position);
        
        log.info("开仓成功: {}", position.getSummary());
        return positionId;
    }
    
    /**
     * 加仓
     * 
     * @param position 现有持仓
     * @param addQuantity 增加数量
     * @param addPrice 增加价格
     * @param leverage 杠杆倍数
     * @return 持仓ID
     */
    private String addToPosition(Position position, BigDecimal addQuantity, BigDecimal addPrice, int leverage) {
        // 计算增加的保证金
        BigDecimal addValue = addQuantity.multiply(addPrice);
        BigDecimal addMargin = addValue.divide(BigDecimal.valueOf(leverage), 8, BigDecimal.ROUND_UP);
        
        // 检查并冻结保证金
        if (!account.freezeMargin(position.getSymbol(), addMargin)) {
            log.warn("加仓失败：保证金不足。需要: {}, 可用: {}", addMargin, account.getAvailableBalance());
            return null;
        }
        
        // 更新持仓
        position.addPosition(addQuantity, addPrice, addMargin);
        
        log.info("加仓成功: {}", position.getSummary());
        return position.getPositionId();
    }
    
    /**
     * 平仓
     * 
     * @param positionId 持仓ID
     * @param closePrice 平仓价格
     * @param fee 手续费
     * @return 实现的盈亏
     */
    public BigDecimal closePosition(String positionId, BigDecimal closePrice, BigDecimal fee) {
        Position position = findPositionById(positionId);
        if (position == null) {
            log.warn("持仓不存在: {}", positionId);
            return BigDecimal.ZERO;
        }
        
        return closePosition(position, position.getQuantity(), closePrice, fee);
    }
    
    /**
     * 部分平仓
     * 
     * @param positionId 持仓ID
     * @param closeQuantity 平仓数量
     * @param closePrice 平仓价格
     * @param fee 手续费
     * @return 实现的盈亏
     */
    public BigDecimal partialClosePosition(String positionId, BigDecimal closeQuantity, 
                                          BigDecimal closePrice, BigDecimal fee) {
        Position position = findPositionById(positionId);
        if (position == null) {
            log.warn("持仓不存在: {}", positionId);
            return BigDecimal.ZERO;
        }
        
        return closePosition(position, closeQuantity, closePrice, fee);
    }
    
    /**
     * 执行平仓操作
     * 
     * @param position 持仓
     * @param closeQuantity 平仓数量
     * @param closePrice 平仓价格
     * @param fee 手续费
     * @return 实现的盈亏
     */
    private BigDecimal closePosition(Position position, BigDecimal closeQuantity, 
                                   BigDecimal closePrice, BigDecimal fee) {
        
        // 计算释放的保证金
        BigDecimal closeRatio = closeQuantity.divide(position.getQuantity(), 8, BigDecimal.ROUND_HALF_UP);
        BigDecimal releaseMargin = position.getMargin().multiply(closeRatio);
        
        // 执行平仓
        BigDecimal realizedPnl = position.partialClose(closeQuantity, closePrice, fee);
        
        // 释放保证金
        account.releaseMargin(position.getSymbol(), releaseMargin);
        
        // 实现盈亏
        account.realizePnl(realizedPnl);
        
        // 如果完全平仓，移动到历史持仓
        if (position.getStatus() == Position.PositionStatus.CLOSED) {
            String positionKey = getPositionKey(position.getSymbol());
            activePositions.remove(positionKey);
            historicalPositions.put(position.getPositionId(), position);
            
            // 清除该交易对的未实现盈亏
            account.updateUnrealizedPnl(position.getSymbol(), BigDecimal.ZERO);
        }
        
        log.info("平仓完成: {} 数量: {}, 价格: {}, 实现盈亏: {}", 
                position.getPositionId(), closeQuantity, closePrice, realizedPnl);
        
        return realizedPnl;
    }
    
    /**
     * 更新持仓价格
     * 
     * @param symbol 交易对
     * @param currentPrice 当前价格
     */
    public void updatePositionPrice(String symbol, BigDecimal currentPrice) {
        String positionKey = getPositionKey(symbol);
        Position position = activePositions.get(positionKey);
        
        if (position != null) {
            position.updatePrice(currentPrice);
            
            // 更新账户的未实现盈亏
            account.updateUnrealizedPnl(symbol, position.getUnrealizedPnl());
            
            // 检查止损止盈
            checkStopLossAndTakeProfit(position);
        }
    }
    
    /**
     * 检查止损止盈
     * 
     * @param position 持仓
     */
    private void checkStopLossAndTakeProfit(Position position) {
        if (position.isStopLossTriggered()) {
            log.info("触发止损: {}", position.getSummary());
            closePosition(position.getPositionId(), position.getStopLossPrice(), BigDecimal.ZERO);
        } else if (position.isTakeProfitTriggered()) {
            log.info("触发止盈: {}", position.getSummary());
            closePosition(position.getPositionId(), position.getTakeProfitPrice(), BigDecimal.ZERO);
        }
    }
    
    /**
     * 设置止损价格
     * 
     * @param positionId 持仓ID
     * @param stopLossPrice 止损价格
     */
    public void setStopLoss(String positionId, BigDecimal stopLossPrice) {
        Position position = findPositionById(positionId);
        if (position != null) {
            position.setStopLossPrice(stopLossPrice);
            log.info("设置止损: {} -> {}", positionId, stopLossPrice);
        }
    }
    
    /**
     * 设置止盈价格
     * 
     * @param positionId 持仓ID
     * @param takeProfitPrice 止盈价格
     */
    public void setTakeProfit(String positionId, BigDecimal takeProfitPrice) {
        Position position = findPositionById(positionId);
        if (position != null) {
            position.setTakeProfitPrice(takeProfitPrice);
            log.info("设置止盈: {} -> {}", positionId, takeProfitPrice);
        }
    }
    
    /**
     * 获取持仓
     * 
     * @param symbol 交易对
     * @return 持仓
     */
    public Position getPosition(String symbol) {
        return activePositions.get(getPositionKey(symbol));
    }
    
    /**
     * 根据ID查找持仓
     * 
     * @param positionId 持仓ID
     * @return 持仓
     */
    public Position findPositionById(String positionId) {
        // 先在活跃持仓中查找
        for (Position position : activePositions.values()) {
            if (position.getPositionId().equals(positionId)) {
                return position;
            }
        }
        
        // 再在历史持仓中查找
        return historicalPositions.get(positionId);
    }
    
    /**
     * 获取所有活跃持仓
     * 
     * @return 活跃持仓集合
     */
    public Collection<Position> getActivePositions() {
        return activePositions.values();
    }
    
    /**
     * 获取所有历史持仓
     * 
     * @return 历史持仓集合
     */
    public Collection<Position> getHistoricalPositions() {
        return historicalPositions.values();
    }
    
    /**
     * 获取活跃持仓数量
     * 
     * @return 活跃持仓数量
     */
    public int getActivePositionCount() {
        return activePositions.size();
    }
    
    /**
     * 获取持仓键
     * 
     * @param symbol 交易对
     * @return 持仓键
     */
    private String getPositionKey(String symbol) {
        return account.getAccountId() + "_" + symbol;
    }
}
