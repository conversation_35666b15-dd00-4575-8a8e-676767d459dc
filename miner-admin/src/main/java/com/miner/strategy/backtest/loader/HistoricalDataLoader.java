package com.miner.strategy.backtest.loader;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.miner.strategy.backtest.config.BacktestConfig;
import com.miner.system.domain.Bar;
import com.miner.system.indicator.KLineEntity;
import com.miner.system.mapper.BarMapper;
import com.miner.system.okx.service.marketData.MarketDataAPIService;
import com.miner.system.okx.service.marketData.impl.MarketDataAPIServiceImpl;
import com.miner.system.okx.service.publicData.PublicDataAPIService;
import com.miner.system.okx.service.publicData.impl.PublicDataAPIServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 历史数据加载器 - TASK-001增强版
 * 支持多数据源、数据质量验证、缓存机制、增量更新
 * <p>
 * 功能特性:
 * 1. 多数据源支持 (CSV, 数据库, 交易所API)
 * 2. 数据质量验证和清洗
 * 3. 内存缓存和持久化缓存
 * 4. 增量数据更新
 * 5. 并行数据加载
 * 6. 数据完整性检查
 */
@Slf4j
@Component
public class HistoricalDataLoader implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    // 数据缓存 - 内存缓存
    private static final Map<String, Map<String, List<KLineEntity>>> DATA_CACHE = new ConcurrentHashMap<>();

    // 缓存过期时间 (毫秒)
    private static final long CACHE_EXPIRE_TIME = 30 * 60 * 1000; // 30分钟

    // 缓存时间戳
    private static final Map<String, Long> CACHE_TIMESTAMPS = new ConcurrentHashMap<>();

    // 线程池用于并行数据加载
    private static final ExecutorService EXECUTOR_SERVICE = Executors.newFixedThreadPool(4);

    // 数据质量统计
    private static final Map<String, DataQualityReport> QUALITY_REPORTS = new ConcurrentHashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        HistoricalDataLoader.applicationContext = applicationContext;
    }

    /**
     * 数据质量报告类
     */
    public static class DataQualityReport {
        private String symbol;
        private String timeframe;
        private int totalRecords;
        private int validRecords;
        private int duplicateRecords;
        private int missingRecords;
        private double completenessRate;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private List<String> issues;

        public DataQualityReport(String symbol, String timeframe) {
            this.symbol = symbol;
            this.timeframe = timeframe;
            this.issues = new ArrayList<>();
        }

        // Getters and Setters
        public String getSymbol() {
            return symbol;
        }

        public String getTimeframe() {
            return timeframe;
        }

        public int getTotalRecords() {
            return totalRecords;
        }

        public void setTotalRecords(int totalRecords) {
            this.totalRecords = totalRecords;
        }

        public int getValidRecords() {
            return validRecords;
        }

        public void setValidRecords(int validRecords) {
            this.validRecords = validRecords;
        }

        public int getDuplicateRecords() {
            return duplicateRecords;
        }

        public void setDuplicateRecords(int duplicateRecords) {
            this.duplicateRecords = duplicateRecords;
        }

        public int getMissingRecords() {
            return missingRecords;
        }

        public void setMissingRecords(int missingRecords) {
            this.missingRecords = missingRecords;
        }

        public double getCompletenessRate() {
            return completenessRate;
        }

        public void setCompletenessRate(double completenessRate) {
            this.completenessRate = completenessRate;
        }

        public LocalDateTime getStartTime() {
            return startTime;
        }

        public void setStartTime(LocalDateTime startTime) {
            this.startTime = startTime;
        }

        public LocalDateTime getEndTime() {
            return endTime;
        }

        public void setEndTime(LocalDateTime endTime) {
            this.endTime = endTime;
        }

        public List<String> getIssues() {
            return issues;
        }

        public void addIssue(String issue) {
            this.issues.add(issue);
        }

        @Override
        public String toString() {
            return String.format("DataQualityReport{symbol='%s', timeframe='%s', completeness=%.2f%%, valid=%d/%d, issues=%d}",
                symbol, timeframe, completenessRate * 100, validRecords, totalRecords, issues.size());
        }
    }

    /**
     * 主要数据加载方法 - 支持缓存和数据质量验证
     *
     * @param config       回测配置
     * @param forceRefresh 是否强制刷新缓存
     * @return 历史K线数据
     */
    public static Map<String, List<KLineEntity>> loadHistoricalData(BacktestConfig config, boolean forceRefresh) {
        long startTime = System.currentTimeMillis();
        log.info("开始加载历史数据，配置: {}", config);

        // 生成缓存键
        String cacheKey = generateCacheKey(config);

        // 检查缓存
        if (!forceRefresh && isCacheValid(cacheKey)) {
            log.info("使用缓存数据，缓存键: {}", cacheKey);
            return DATA_CACHE.get(cacheKey);
        }

        Map<String, List<KLineEntity>> historicalData = new HashMap<>();

        try {
            // 根据配置选择数据源
            String dataSource = config.getDataSource() != null ? config.getDataSource() : "database";

            switch (dataSource.toLowerCase()) {
                case "csv":
                    historicalData = loadFromCsvEnhanced(config);
                    break;
                case "exchange":
                case "api":
                    historicalData = loadFromExchangeEnhanced(config);
                    break;
                case "database":
                default:
                    historicalData = loadFromDatabaseEnhanced(config);
                    break;
            }

            // 数据质量验证和清洗
            historicalData = validateAndCleanData(historicalData, config);

            // 更新缓存
            updateCache(cacheKey, historicalData);

            // 生成数据质量报告
            generateQualityReports(historicalData, config);

            long endTime = System.currentTimeMillis();
            log.info("历史数据加载完成，耗时: {}ms, 交易对数量: {}",
                endTime - startTime, historicalData.size());

        } catch (Exception e) {
            log.error("加载历史数据失败", e);
        }

        return historicalData;
    }

    /**
     * 便捷方法 - 使用缓存加载数据
     */
    public static Map<String, List<KLineEntity>> loadHistoricalData(BacktestConfig config) {
        return loadHistoricalData(config, false);
    }

    /**
     * 生成缓存键
     */
    private static String generateCacheKey(BacktestConfig config) {
        String keyBuilder = config.getDataSource() + "_" +
            String.join(",", config.getSymbols()) + "_" +
            config.getMainTimeframe() + "_" +
            config.getSubTimeframe() + "_" +
            config.getStartDate() + "_" +
            config.getEndDate();
        return keyBuilder.replaceAll("[^a-zA-Z0-9_-]", "_");
    }

    /**
     * 检查缓存是否有效
     */
    private static boolean isCacheValid(String cacheKey) {
        if (!DATA_CACHE.containsKey(cacheKey)) {
            return false;
        }

        Long timestamp = CACHE_TIMESTAMPS.get(cacheKey);
        if (timestamp == null) {
            return false;
        }

        return System.currentTimeMillis() - timestamp < CACHE_EXPIRE_TIME;
    }

    /**
     * 更新缓存
     */
    private static void updateCache(String cacheKey, Map<String, List<KLineEntity>> data) {
        DATA_CACHE.put(cacheKey, data);
        CACHE_TIMESTAMPS.put(cacheKey, System.currentTimeMillis());
        log.debug("缓存已更新，键: {}, 数据量: {}", cacheKey, data.size());
    }

    /**
     * 清理过期缓存
     */
    public static void cleanExpiredCache() {
        long currentTime = System.currentTimeMillis();
        List<String> expiredKeys = new ArrayList<>();

        for (Map.Entry<String, Long> entry : CACHE_TIMESTAMPS.entrySet()) {
            if (currentTime - entry.getValue() > CACHE_EXPIRE_TIME) {
                expiredKeys.add(entry.getKey());
            }
        }

        for (String key : expiredKeys) {
            DATA_CACHE.remove(key);
            CACHE_TIMESTAMPS.remove(key);
        }

        if (!expiredKeys.isEmpty()) {
            log.info("清理了 {} 个过期缓存", expiredKeys.size());
        }
    }

    /**
     * 数据质量验证和清洗
     */
    private static Map<String, List<KLineEntity>> validateAndCleanData(
        Map<String, List<KLineEntity>> rawData, BacktestConfig config) {

        Map<String, List<KLineEntity>> cleanedData = new HashMap<>();

        for (Map.Entry<String, List<KLineEntity>> entry : rawData.entrySet()) {
            String symbol = entry.getKey();
            List<KLineEntity> klines = entry.getValue();

            log.info("开始验证和清洗 {} 的数据，原始数据量: {}", symbol, klines.size());

            // 数据验证和清洗
            List<KLineEntity> validKlines = new ArrayList<>();
            Set<Long> timestamps = new HashSet<>();
            int duplicateCount = 0;
            int invalidCount = 0;

            for (KLineEntity kline : klines) {
                // 基础数据验证
                if (!isValidKLine(kline)) {
                    invalidCount++;
                    continue;
                }

                // 去重
                if (timestamps.contains(kline.getTs())) {
                    duplicateCount++;
                    continue;
                }

                timestamps.add(kline.getTs());
                validKlines.add(kline);
            }

            // 按时间排序
            validKlines.sort((k1, k2) -> Long.compare(k2.getTs(), k1.getTs()));

            // 检查数据连续性
            int missingCount = checkDataContinuity(validKlines, config);

            cleanedData.put(symbol, validKlines);

            log.info("{} 数据清洗完成: 有效={}, 重复={}, 无效={}, 缺失={}",
                symbol, validKlines.size(), duplicateCount, invalidCount, missingCount);
        }

        return cleanedData;
    }

    /**
     * 验证单个K线数据的有效性
     */
    private static boolean isValidKLine(KLineEntity kline) {
        if (kline == null) return false;
        if (kline.getOpen() == null || kline.getHigh() == null ||
            kline.getLow() == null || kline.getClose() == null) return false;
        if (kline.getTs() <= 0) return false;

        // 价格合理性检查
        double open = kline.getOpen().doubleValue();
        double high = kline.getHigh().doubleValue();
        double low = kline.getLow().doubleValue();
        double close = kline.getClose().doubleValue();

        if (open <= 0 || high <= 0 || low <= 0 || close <= 0) return false;
        if (high < Math.max(open, close) || low > Math.min(open, close)) return false;
        if (high < low) return false;

        // 异常波动检查 (单根K线涨跌幅不超过50%)
        double maxPrice = Math.max(open, close);
        double minPrice = Math.min(open, close);
        if ((maxPrice - minPrice) / minPrice > 0.5) return false;

        return true;
    }

    /**
     * 检查数据连续性
     */
    private static int checkDataContinuity(List<KLineEntity> klines, BacktestConfig config) {
        if (klines.size() < 2) return 0;

        // 获取时间间隔
        String timeframe = klines.get(0).getBar();
        int intervalSeconds = getIntervalSeconds(timeframe);

        int missingCount = 0;
        for (int i = 0; i < klines.size() - 1; i++) {
            long currentTs = klines.get(i).getTs();
            long nextTs = klines.get(i + 1).getTs();
            long expectedInterval = intervalSeconds;
            long actualInterval = Math.abs(currentTs - nextTs);

            // 允许一定的时间误差
            if (actualInterval > expectedInterval * 1.5) {
                missingCount += (int) (actualInterval / expectedInterval) - 1;
            }
        }

        return missingCount;
    }

    /**
     * 增强版数据库数据加载 - 支持并行加载和增量更新
     */
    private static Map<String, List<KLineEntity>> loadFromDatabaseEnhanced(BacktestConfig config) {
        Map<String, List<KLineEntity>> historicalData = new ConcurrentHashMap<>();

        try {
            if (applicationContext == null) {
                log.error("应用上下文未初始化，无法获取BarMapper");
                return historicalData;
            }

            BarMapper barMapper = applicationContext.getBean(BarMapper.class);
            if (barMapper == null) {
                log.error("无法获取BarMapper实例");
                return historicalData;
            }

            // 获取交易对列表
            List<String> symbols = config.getSymbols();
            if (symbols == null || symbols.isEmpty()) {
                symbols = Arrays.asList("BTC-USDT-SWAP", "ETH-USDT-SWAP");
                log.info("未指定交易对，使用默认交易对：{}", symbols);
            }

            // 时间格式化
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String startDate = config.getStartDate() != null ?
                config.getStartDate().format(formatter) :
                LocalDateTime.now().minusYears(5).format(formatter); // 默认5年数据

            String endDate = config.getEndDate() != null ?
                config.getEndDate().format(formatter) :
                LocalDateTime.now().format(formatter);

            log.info("从数据库加载历史数据，时间范围: {} 至 {}", startDate, endDate);

            // 获取K线周期
            List<String> timeframes = new ArrayList<>();
            timeframes.add(config.getMainTimeframe().toUpperCase());
            if (!config.getMainTimeframe().equalsIgnoreCase(config.getSubTimeframe())) {
                timeframes.add(config.getSubTimeframe().toUpperCase());
            }

            // 使用CompletableFuture并行加载数据
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (String symbol : symbols) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    List<KLineEntity> symbolKlines = new ArrayList<>();

                    for (String timeframe : timeframes) {
                        log.info("正在加载 {} 的 {} 周期数据...", symbol, timeframe);

                        try {
                            // 分批加载大量数据
                            List<Bar> bars = loadDataInBatches(barMapper, symbol, timeframe, startDate, endDate);

                            if (bars == null || bars.isEmpty()) {
                                log.warn("未找到 {} 的 {} 周期数据", symbol, timeframe);
                                continue;
                            }

                            // 并行转换数据
                            List<KLineEntity> klines = bars.parallelStream()
                                .map(bar -> convertBarToKLine(bar, timeframe))
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());

                            symbolKlines.addAll(klines);
                            log.info("成功加载 {} 的 {} 周期数据，共 {} 条", symbol, timeframe, klines.size());

                        } catch (Exception e) {
                            log.error("加载 {} 的 {} 周期数据失败", symbol, timeframe, e);
                        }
                    }

                    // 按时间戳降序排序
                    symbolKlines.sort((k1, k2) -> Long.compare(k2.getTs(), k1.getTs()));

                    if (!symbolKlines.isEmpty()) {
                        historicalData.put(symbol, symbolKlines);
                    }

                }, EXECUTOR_SERVICE);

                futures.add(future);
            }

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            log.info("从数据库加载了 {} 个交易对的历史数据", historicalData.size());

        } catch (Exception e) {
            log.error("从数据库加载历史数据失败: {}", e.getMessage(), e);
        }

        return historicalData;
    }

    /**
     * 分批加载数据以避免内存溢出
     */
    private static List<Bar> loadDataInBatches(BarMapper barMapper, String symbol, String timeframe,
                                               String startDate, String endDate) {
        List<Bar> allBars = new ArrayList<>();

        try {
            // 计算批次大小 (每批最多10000条记录)
            int batchSize = 10000;
            int offset = 0;

            while (true) {
                List<Bar> batchBars = DataLoaderUtils.getBarsWithLimit(barMapper, symbol, timeframe, startDate, endDate, offset, batchSize);

                if (batchBars == null || batchBars.isEmpty()) {
                    break;
                }

                allBars.addAll(batchBars);
                offset += batchSize;

                log.debug("已加载 {} 的 {} 数据批次，当前总数: {}", symbol, timeframe, allBars.size());

                // 如果返回的数据少于批次大小，说明已经加载完毕
                if (batchBars.size() < batchSize) {
                    break;
                }
            }

        } catch (Exception e) {
            log.error("分批加载数据失败", e);
            // 如果分批加载失败，尝试一次性加载
            try {
                allBars = barMapper.getBars(symbol, timeframe, startDate, endDate);
            } catch (Exception ex) {
                log.error("一次性加载数据也失败", ex);
            }
        }

        return allBars;
    }

    /**
     * 转换Bar对象为KLineEntity
     */
    private static KLineEntity convertBarToKLine(Bar bar, String timeframe) {
        try {
            KLineEntity kline = new KLineEntity();
            kline.setInstId(bar.getInstId());
            kline.setTs(bar.getTs());
            kline.setOpen(new BigDecimal(bar.getOpen()));
            kline.setHigh(new BigDecimal(bar.getHigh()));
            kline.setLow(new BigDecimal(bar.getLow()));
            kline.setClose(new BigDecimal(bar.getClose()));
            kline.setVolume(bar.getVol().floatValue());
            kline.setBar(timeframe.toLowerCase());
            kline.setDate(DateUtil.formatDateTime(bar.getDate()));
            return kline;
        } catch (Exception e) {
            log.warn("转换Bar数据失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 增强版CSV数据加载 - 支持多文件和数据验证
     */
    private static Map<String, List<KLineEntity>> loadFromCsvEnhanced(BacktestConfig config) {
        Map<String, List<KLineEntity>> historicalData = new ConcurrentHashMap<>();

        String csvPath = config.getCsvFilePath();
        if (csvPath == null || csvPath.isEmpty()) {
            log.error("CSV文件路径未配置");
            return historicalData;
        }

        log.info("开始从CSV加载数据: {}", csvPath);

        try {
            // 支持多个CSV文件
            String[] csvFiles = csvPath.split(",");

            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (String csvFile : csvFiles) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    Map<String, List<KLineEntity>> fileData = loadSingleCsvFile(csvFile.trim(), config);

                    // 合并数据
                    synchronized (historicalData) {
                        for (Map.Entry<String, List<KLineEntity>> entry : fileData.entrySet()) {
                            historicalData.computeIfAbsent(entry.getKey(), k -> new ArrayList<>())
                                .addAll(entry.getValue());
                        }
                    }
                }, EXECUTOR_SERVICE);

                futures.add(future);
            }

            // 等待所有文件加载完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 对每个交易对的数据进行排序和去重
            for (Map.Entry<String, List<KLineEntity>> entry : historicalData.entrySet()) {
                List<KLineEntity> klines = entry.getValue();

                // 去重并排序
                Map<Long, KLineEntity> uniqueKlines = new LinkedHashMap<>();
                for (KLineEntity kline : klines) {
                    uniqueKlines.put(kline.getTs(), kline);
                }

                List<KLineEntity> sortedKlines = new ArrayList<>(uniqueKlines.values());
                sortedKlines.sort((k1, k2) -> Long.compare(k2.getTs(), k1.getTs()));

                entry.setValue(sortedKlines);
            }

            log.info("CSV数据加载完成，交易对数量: {}", historicalData.size());

        } catch (Exception e) {
            log.error("CSV数据加载失败", e);
        }

        return historicalData;
    }

    /**
     * 加载单个CSV文件
     */
    private static Map<String, List<KLineEntity>> loadSingleCsvFile(String csvFilePath, BacktestConfig config) {
        Map<String, List<KLineEntity>> fileData = new HashMap<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(csvFilePath))) {
            String line;
            boolean isHeader = true;
            int lineNumber = 0;
            int validLines = 0;
            int invalidLines = 0;

            while ((line = reader.readLine()) != null) {
                lineNumber++;

                if (isHeader) {
                    isHeader = false;
                    continue;
                }

                try {
                    KLineEntity kline = parseCsvLine(line, config);
                    if (kline != null) {
                        fileData.computeIfAbsent(kline.getInstId(), k -> new ArrayList<>()).add(kline);
                        validLines++;
                    } else {
                        invalidLines++;
                    }
                } catch (Exception e) {
                    log.warn("解析CSV行失败，行号: {}, 内容: {}, 错误: {}", lineNumber, line, e.getMessage());
                    invalidLines++;
                }
            }

            log.info("CSV文件 {} 加载完成: 总行数={}, 有效行数={}, 无效行数={}",
                csvFilePath, lineNumber, validLines, invalidLines);

        } catch (IOException e) {
            log.error("读取CSV文件失败: {}", csvFilePath, e);
        }

        return fileData;
    }

    /**
     * 解析CSV行数据
     */
    private static KLineEntity parseCsvLine(String line, BacktestConfig config) {
        String[] fields = line.split(",");
        if (fields.length < 8) {
            return null;
        }

        try {
            String symbol = fields[0].trim();
            long timestamp = Long.parseLong(fields[1].trim());
            double open = Double.parseDouble(fields[2].trim());
            double high = Double.parseDouble(fields[3].trim());
            double low = Double.parseDouble(fields[4].trim());
            double close = Double.parseDouble(fields[5].trim());
            float volume = Float.parseFloat(fields[6].trim());
            String timeframe = fields[7].trim();

            // 过滤交易对
            if (config.getSymbols() != null && !config.getSymbols().isEmpty()) {
                if (!config.getSymbols().contains(symbol)) {
                    return null;
                }
            }

            // 过滤K线周期
            if (!timeframe.equals(config.getMainTimeframe()) && !timeframe.equals(config.getSubTimeframe())) {
                return null;
            }

            // 转换为LocalDateTime
            LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());

            // 过滤日期范围
            if (config.getStartDate() != null && dateTime.isBefore(config.getStartDate())) {
                return null;
            }
            if (config.getEndDate() != null && dateTime.isAfter(config.getEndDate())) {
                return null;
            }

            // 创建KLineEntity对象
            KLineEntity kline = new KLineEntity();
            kline.setInstId(symbol);
            kline.setTs(timestamp / 1000); // 转换为秒
            kline.setOpen(BigDecimal.valueOf(open));
            kline.setHigh(BigDecimal.valueOf(high));
            kline.setLow(BigDecimal.valueOf(low));
            kline.setClose(BigDecimal.valueOf(close));
            kline.setVolume(volume);
            kline.setBar(timeframe);

            return kline;

        } catch (Exception e) {
            log.debug("解析CSV行数据失败: {}", line, e);
            return null;
        }
    }

    /**
     * 增强版交易所数据加载 - 支持重试和错误恢复
     */
    private static Map<String, List<KLineEntity>> loadFromExchangeEnhanced(BacktestConfig config) {
        Map<String, List<KLineEntity>> historicalData = new ConcurrentHashMap<>();

        // 初始化交易所API服务
        PublicDataAPIService publicDataAPIService = new PublicDataAPIServiceImpl();
        MarketDataAPIService marketDataAPIService = new MarketDataAPIServiceImpl();

        try {
            // 获取交易对列表
            List<String> symbols = config.getSymbols();
            if (symbols == null || symbols.isEmpty()) {
                symbols = Arrays.asList("BTC-USDT-SWAP", "ETH-USDT-SWAP");
                log.info("未指定交易对，使用默认交易对：{}", symbols);
            }

            // 确定时间范围
            long startTimestamp = config.getStartDate() != null ?
                config.getStartDate().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() :
                Instant.now().minus(5 * 365, ChronoUnit.DAYS).toEpochMilli(); // 默认5年

            long endTimestamp = config.getEndDate() != null ?
                config.getEndDate().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() :
                Instant.now().toEpochMilli();

            log.info("从交易所获取历史数据，时间范围: {} 到 {}",
                LocalDateTime.ofInstant(Instant.ofEpochMilli(startTimestamp), ZoneId.systemDefault()),
                LocalDateTime.ofInstant(Instant.ofEpochMilli(endTimestamp), ZoneId.systemDefault()));

            // 获取所需的K线周期
            List<String> timeframes = new ArrayList<>();
            timeframes.add(config.getMainTimeframe());
            if (!config.getMainTimeframe().equals(config.getSubTimeframe())) {
                timeframes.add(config.getSubTimeframe());
            }

            // 并行获取数据
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (String symbol : symbols) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    for (String timeframe : timeframes) {
                        try {
                            List<KLineEntity> klines = fetchKlinesFromExchange(
                                marketDataAPIService, symbol, timeframe, startTimestamp, endTimestamp);

                            if (!klines.isEmpty()) {
                                synchronized (historicalData) {
                                    historicalData.computeIfAbsent(symbol, k -> new ArrayList<>()).addAll(klines);
                                }
                                log.info("成功获取 {} {} 数据，共 {} 条", symbol, timeframe, klines.size());
                            }
                        } catch (Exception e) {
                            log.error("获取 {} {} 数据失败", symbol, timeframe, e);
                        }
                    }
                }, EXECUTOR_SERVICE);

                futures.add(future);
            }

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 对数据进行排序
            for (Map.Entry<String, List<KLineEntity>> entry : historicalData.entrySet()) {
                entry.getValue().sort((k1, k2) -> Long.compare(k2.getTs(), k1.getTs()));
            }

            log.info("从交易所加载了 {} 个交易对的历史数据", historicalData.size());

        } catch (Exception e) {
            log.error("从交易所加载历史数据失败", e);
        }

        return historicalData;
    }

    /**
     * 从交易所获取K线数据，支持重试机制
     */
    private static List<KLineEntity> fetchKlinesFromExchange(MarketDataAPIService marketDataAPIService,
                                                             String symbol, String timeframe,
                                                             long startTimestamp, long endTimestamp) {
        List<KLineEntity> allKlines = new ArrayList<>();

        // 将时间周期转换为OKX API格式
        String okxBar = convertTimeframeToOkxFormat(timeframe);

        // 按时间段分批获取数据
        long step = getStepSize(timeframe);
        int maxRetries = 3;

        for (long timestamp = startTimestamp; timestamp < endTimestamp; timestamp += step) {
            long batchEndTs = Math.min(timestamp + step, endTimestamp);

            boolean success = false;
            int retryCount = 0;

            while (!success && retryCount < maxRetries) {
                try {
                    // 调用OKX API获取K线数据
                    JSONObject barJson = marketDataAPIService.getCandlesticksHistory(
                        symbol,
                        String.valueOf(batchEndTs),
                        String.valueOf(timestamp),
                        okxBar,
                        "100"
                    );

                    // 解析K线数据
                    List<KLineEntity> batchKlines = parseKlines(barJson, symbol, timeframe);
                    allKlines.addAll(batchKlines);

                    success = true;

                    // 避免频繁请求
                    ThreadUtil.sleep(100);

                } catch (Exception e) {
                    retryCount++;
                    log.warn("获取 {} {} 数据失败，重试 {}/{}: {}",
                        symbol, timeframe, retryCount, maxRetries, e.getMessage());

                    if (retryCount < maxRetries) {
                        ThreadUtil.sleep(1000 * retryCount); // 递增延迟
                    }
                }
            }

            if (!success) {
                log.error("获取 {} {} 时间段 {} - {} 的数据最终失败",
                    symbol, timeframe, timestamp, batchEndTs);
            }
        }

        return allKlines;
    }

    /**
     * 生成数据质量报告
     */
    private static void generateQualityReports(Map<String, List<KLineEntity>> historicalData, BacktestConfig config) {
        for (Map.Entry<String, List<KLineEntity>> entry : historicalData.entrySet()) {
            String symbol = entry.getKey();
            List<KLineEntity> klines = entry.getValue();

            // 按时间框架分组
            Map<String, List<KLineEntity>> timeframeGroups = klines.stream()
                .collect(Collectors.groupingBy(KLineEntity::getBar));

            for (Map.Entry<String, List<KLineEntity>> tfEntry : timeframeGroups.entrySet()) {
                String timeframe = tfEntry.getKey();
                List<KLineEntity> tfKlines = tfEntry.getValue();

                DataQualityReport report = new DataQualityReport(symbol, timeframe);

                // 基础统计
                report.setTotalRecords(tfKlines.size());
                report.setValidRecords(tfKlines.size()); // 已经过验证的数据

                // 时间范围
                if (!tfKlines.isEmpty()) {
                    tfKlines.sort((k1, k2) -> Long.compare(k1.getTs(), k2.getTs()));
                    report.setStartTime(LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(tfKlines.get(0).getTs()), ZoneId.systemDefault()));
                    report.setEndTime(LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(tfKlines.get(tfKlines.size() - 1).getTs()), ZoneId.systemDefault()));
                }

                // 计算完整性
                int expectedRecords = calculateExpectedRecords(report.getStartTime(), report.getEndTime(), timeframe);
                report.setMissingRecords(Math.max(0, expectedRecords - tfKlines.size()));
                report.setCompletenessRate((double) tfKlines.size() / expectedRecords);

                // 数据质量检查
                performQualityChecks(report, tfKlines);

                // 保存报告
                String reportKey = symbol + "_" + timeframe;
                QUALITY_REPORTS.put(reportKey, report);

                log.info("数据质量报告: {}", report);
            }
        }
    }

    /**
     * 计算预期记录数
     */
    private static int calculateExpectedRecords(LocalDateTime startTime, LocalDateTime endTime, String timeframe) {
        if (startTime == null || endTime == null) return 0;

        long totalSeconds = ChronoUnit.SECONDS.between(startTime, endTime);
        int intervalSeconds = getIntervalSeconds(timeframe);

        return (int) (totalSeconds / intervalSeconds);
    }

    /**
     * 执行数据质量检查
     */
    private static void performQualityChecks(DataQualityReport report, List<KLineEntity> klines) {
        if (klines.isEmpty()) return;

        // 检查价格异常
        int priceAnomalies = 0;
        for (KLineEntity kline : klines) {
            double open = kline.getOpen().doubleValue();
            double high = kline.getHigh().doubleValue();
            double low = kline.getLow().doubleValue();
            double close = kline.getClose().doubleValue();

            // 检查异常波动 (超过20%的单K线波动)
            double maxChange = Math.max(Math.abs(high - open), Math.abs(low - open)) / open;
            if (maxChange > 0.2) {
                priceAnomalies++;
            }
        }

        if (priceAnomalies > 0) {
            report.addIssue(String.format("发现 %d 个价格异常K线", priceAnomalies));
        }

        // 检查成交量异常
        List<Double> volumes = klines.stream()
            .mapToDouble(k -> k.getVolume())
            .boxed()
            .collect(Collectors.toList());

        if (!volumes.isEmpty()) {
            double avgVolume = volumes.stream().mapToDouble(Double::doubleValue).average().orElse(0);
            long zeroVolumeCount = volumes.stream().mapToLong(v -> v == 0 ? 1 : 0).sum();

            if (zeroVolumeCount > klines.size() * 0.1) {
                report.addIssue(String.format("零成交量K线过多: %d/%d", zeroVolumeCount, klines.size()));
            }
        }

        // 检查时间连续性
        klines.sort((k1, k2) -> Long.compare(k1.getTs(), k2.getTs()));
        int intervalSeconds = getIntervalSeconds(report.getTimeframe());
        int gapCount = 0;

        for (int i = 1; i < klines.size(); i++) {
            long gap = klines.get(i).getTs() - klines.get(i - 1).getTs();
            if (gap > intervalSeconds * 1.5) {
                gapCount++;
            }
        }

        if (gapCount > 0) {
            report.addIssue(String.format("发现 %d 个时间间隔异常", gapCount));
        }
    }

    /**
     * 获取数据质量报告
     */
    public static DataQualityReport getQualityReport(String symbol, String timeframe) {
        String key = symbol + "_" + timeframe;
        return QUALITY_REPORTS.get(key);
    }

    /**
     * 获取所有数据质量报告
     */
    public static Map<String, DataQualityReport> getAllQualityReports() {
        return new HashMap<>(QUALITY_REPORTS);
    }

    /**
     * 增量数据更新 - 获取最新数据并合并到现有数据中
     */
    public static Map<String, List<KLineEntity>> updateIncrementalData(BacktestConfig config,
                                                                       Map<String, List<KLineEntity>> existingData) {
        log.info("开始增量数据更新");

        // 确定增量更新的起始时间
        LocalDateTime incrementalStartTime = getLatestDataTime(existingData);
        if (incrementalStartTime == null) {
            log.info("没有现有数据，执行全量加载");
            return loadHistoricalData(config, true);
        }

        // 创建增量配置
        BacktestConfig incrementalConfig = createIncrementalConfig(config, incrementalStartTime);

        // 加载增量数据
        Map<String, List<KLineEntity>> incrementalData = loadHistoricalData(incrementalConfig, true);

        // 合并数据
        Map<String, List<KLineEntity>> mergedData = mergeData(existingData, incrementalData);

        log.info("增量数据更新完成，新增数据量: {}",
            incrementalData.values().stream().mapToInt(List::size).sum());

        return mergedData;
    }

    /**
     * 获取现有数据的最新时间
     */
    private static LocalDateTime getLatestDataTime(Map<String, List<KLineEntity>> existingData) {
        LocalDateTime latestTime = null;

        for (List<KLineEntity> klines : existingData.values()) {
            if (klines.isEmpty()) continue;

            // 数据已按时间降序排列，第一个就是最新的
            LocalDateTime dataTime = LocalDateTime.ofInstant(
                Instant.ofEpochSecond(klines.get(0).getTs()), ZoneId.systemDefault());

            if (latestTime == null || dataTime.isAfter(latestTime)) {
                latestTime = dataTime;
            }
        }

        return latestTime;
    }

    /**
     * 创建增量配置
     */
    private static BacktestConfig createIncrementalConfig(BacktestConfig originalConfig, LocalDateTime startTime) {
        BacktestConfig incrementalConfig = new BacktestConfig();
        incrementalConfig.setSymbols(originalConfig.getSymbols());
        incrementalConfig.setMainTimeframe(originalConfig.getMainTimeframe());
        incrementalConfig.setSubTimeframe(originalConfig.getSubTimeframe());
        incrementalConfig.setDataSource(originalConfig.getDataSource());
        incrementalConfig.setCsvFilePath(originalConfig.getCsvFilePath());

        // 设置增量时间范围
        incrementalConfig.setStartDate(startTime);
        incrementalConfig.setEndDate(LocalDateTime.now());

        return incrementalConfig;
    }

    /**
     * 合并现有数据和增量数据
     */
    private static Map<String, List<KLineEntity>> mergeData(Map<String, List<KLineEntity>> existingData,
                                                            Map<String, List<KLineEntity>> incrementalData) {
        Map<String, List<KLineEntity>> mergedData = new HashMap<>();

        // 合并所有交易对的数据
        Set<String> allSymbols = new HashSet<>();
        allSymbols.addAll(existingData.keySet());
        allSymbols.addAll(incrementalData.keySet());

        for (String symbol : allSymbols) {
            List<KLineEntity> existing = existingData.getOrDefault(symbol, new ArrayList<>());
            List<KLineEntity> incremental = incrementalData.getOrDefault(symbol, new ArrayList<>());

            // 合并并去重
            Map<Long, KLineEntity> uniqueKlines = new LinkedHashMap<>();

            // 添加现有数据
            for (KLineEntity kline : existing) {
                uniqueKlines.put(kline.getTs(), kline);
            }

            // 添加增量数据
            for (KLineEntity kline : incremental) {
                uniqueKlines.put(kline.getTs(), kline);
            }

            // 排序
            List<KLineEntity> mergedKlines = new ArrayList<>(uniqueKlines.values());
            mergedKlines.sort((k1, k2) -> Long.compare(k2.getTs(), k1.getTs()));

            mergedData.put(symbol, mergedKlines);
        }

        return mergedData;
    }

    /**
     * 从CSV文件加载历史数据
     * 典型的CSV格式: symbol,timestamp,open,high,low,close,volume,timeframe
     * 例如: BTC-USDT,1648483200,47100.5,47350.2,46800.3,47120.8,1234.5,4h
     *
     * @param csvFilePath CSV文件路径
     * @param config      回测配置
     * @return 历史K线数据
     */
    public static Map<String, List<KLineEntity>> loadFromCsv(String csvFilePath, BacktestConfig config) {
        Map<String, List<KLineEntity>> historicalData = new HashMap<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(csvFilePath))) {
            String line;
            boolean isHeader = true;

            while ((line = reader.readLine()) != null) {
                if (isHeader) {
                    isHeader = false;
                    continue;
                }

                String[] fields = line.split(",");
                if (fields.length < 8) {
                    log.warn("忽略无效数据行: {}", line);
                    continue;
                }

                String symbol = fields[0];
                long timestamp = Long.parseLong(fields[1]);
                double open = Double.parseDouble(fields[2]);
                double high = Double.parseDouble(fields[3]);
                double low = Double.parseDouble(fields[4]);
                double close = Double.parseDouble(fields[5]);
                float volume = Float.parseFloat(fields[6]);
                String timeframe = fields[7];

                // 过滤交易对
                if (config.getSymbols() != null && !config.getSymbols().isEmpty()) {
                    if (!config.getSymbols().contains(symbol)) {
                        continue;
                    }
                }

                // 过滤K线周期
                if (!timeframe.equals(config.getMainTimeframe()) && !timeframe.equals(config.getSubTimeframe())) {
                    continue;
                }

                // 转换为LocalDateTime
                LocalDateTime dateTime = LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());

                // 过滤日期范围
                if (config.getStartDate() != null && dateTime.isBefore(config.getStartDate())) {
                    continue;
                }
                if (config.getEndDate() != null && dateTime.isAfter(config.getEndDate())) {
                    continue;
                }

                // 创建KLineEntity对象
                KLineEntity kline = new KLineEntity();
                kline.setInstId(symbol);
                kline.setTs(timestamp);
                kline.setOpen(BigDecimal.valueOf(open));
                kline.setHigh(BigDecimal.valueOf(high));
                kline.setLow(BigDecimal.valueOf(low));
                kline.setClose(BigDecimal.valueOf(close));
                kline.setVolume(volume);
                kline.setBar(timeframe);

                // 添加到对应的交易对列表
                historicalData
                    .computeIfAbsent(symbol, k -> new ArrayList<>())
                    .add(kline);
            }

            // 对每个交易对的K线数据按时间排序（最新的排在前面）
            for (List<KLineEntity> klines : historicalData.values()) {
                klines.sort((k1, k2) -> Long.compare(k2.getTs(), k1.getTs()));
            }

            log.info("从CSV加载了{}个交易对的历史数据", historicalData.size());

        } catch (IOException e) {
            log.error("加载CSV历史数据失败", e);
        }

        return historicalData;
    }

    /**
     * 从数据库加载历史数据
     *
     * @param config 回测配置
     * @return 历史K线数据
     */
    public static Map<String, List<KLineEntity>> loadFromDatabase(BacktestConfig config) {
        Map<String, List<KLineEntity>> historicalData = new HashMap<>();

        try {
            if (applicationContext == null) {
                log.error("应用上下文未初始化，无法获取BarMapper");
                return historicalData;
            }

            BarMapper barMapper = applicationContext.getBean(BarMapper.class);
            if (barMapper == null) {
                log.error("无法获取BarMapper实例");
                return historicalData;
            }

            // 获取交易对列表
            List<String> symbols = config.getSymbols();
            if (symbols == null || symbols.isEmpty()) {
                symbols = Arrays.asList("BTC-USDT-SWAP", "ETH-USDT-SWAP");
                log.info("未指定交易对，使用默认交易对：{}", symbols);
            }

            // 时间格式化
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 时间范围
            String startDate = config.getStartDate() != null ?
                config.getStartDate().format(formatter) :
                LocalDateTime.now().minusDays(30).format(formatter);

            String endDate = config.getEndDate() != null ?
                config.getEndDate().format(formatter) :
                LocalDateTime.now().format(formatter);

            log.info("从数据库加载历史数据，时间范围: {} 至 {}", startDate, endDate);

            // 获取K线周期
            List<String> timeframes = new ArrayList<>();
            timeframes.add(config.getMainTimeframe().toUpperCase());
            if (!config.getMainTimeframe().equalsIgnoreCase(config.getSubTimeframe())) {
                timeframes.add(config.getSubTimeframe().toUpperCase());
            }

            // 使用并行流处理多个交易对
            symbols.parallelStream().forEach(symbol -> {
                List<KLineEntity> symbolKlines = new ArrayList<>();

                for (String timeframe : timeframes) {
                    log.info("正在加载 {} 的 {} 周期数据...", symbol, timeframe);

                    // 批量获取数据
                    List<Bar> bars = barMapper.getBars(symbol, timeframe, startDate, endDate);
                    if (bars == null || bars.isEmpty()) {
                        log.warn("未找到 {} 的 {} 周期数据", symbol, timeframe);
                        continue;
                    }

                    // 使用并行流转换数据
                    List<KLineEntity> klines = bars.parallelStream()
                        .map(bar -> {
                            KLineEntity kline = new KLineEntity();
                            kline.setInstId(bar.getInstId());
                            kline.setTs(bar.getTs());
                            kline.setOpen(new BigDecimal(bar.getOpen()));
                            kline.setHigh(new BigDecimal(bar.getHigh()));
                            kline.setLow(new BigDecimal(bar.getLow()));
                            kline.setClose(new BigDecimal(bar.getClose()));
                            kline.setVolume(bar.getVol().floatValue());
                            kline.setBar(timeframe.toLowerCase());
                            kline.setDate(DateUtil.formatDateTime(bar.getDate()));
                            return kline;
                        })
                        .collect(Collectors.toList());

                    symbolKlines.addAll(klines);
                    log.info("成功加载 {} 的 {} 周期数据，共 {} 条", symbol, timeframe, klines.size());
                }

                // 按时间戳降序排序
                symbolKlines.sort((k1, k2) -> Long.compare(k2.getTs(), k1.getTs()));

                // 使用线程安全的方式添加到结果中
                synchronized (historicalData) {
                    if (!symbolKlines.isEmpty()) {
                        historicalData.put(symbol, symbolKlines);
                    }
                }
            });

            log.info("从数据库加载了 {} 个交易对的历史数据", historicalData.size());

        } catch (Exception e) {
            log.error("从数据库加载历史数据失败: {}", e.getMessage(), e);
        }

        return historicalData;
    }

    /**
     * 使用API从交易所加载历史数据
     *
     * @param config 回测配置
     * @return 历史K线数据
     */
    public static Map<String, List<KLineEntity>> loadFromExchange(BacktestConfig config) {
        Map<String, List<KLineEntity>> historicalData = new HashMap<>();

        // 初始化交易所API服务
        PublicDataAPIService publicDataAPIService = new PublicDataAPIServiceImpl();
        MarketDataAPIService marketDataAPIService = new MarketDataAPIServiceImpl();

        try {
            // 获取交易对列表
            List<String> symbols;
            if (config.getSymbols() != null && !config.getSymbols().isEmpty()) {
                symbols = config.getSymbols();
            } else {
                symbols = getInstIds(publicDataAPIService, marketDataAPIService, 20);
            }

            // 确定时间范围
            long startTimestamp = config.getStartDate() != null ?
                config.getStartDate().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() :
                Instant.now().minus(30, ChronoUnit.DAYS).toEpochMilli();

            long endTimestamp = config.getEndDate() != null ?
                config.getEndDate().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() :
                Instant.now().toEpochMilli();
            log.info("从 {} 到 {} 的时间范围中获取历史数据",
                LocalDateTime.ofInstant(Instant.ofEpochMilli(startTimestamp), ZoneId.systemDefault()),
                LocalDateTime.ofInstant(Instant.ofEpochMilli(endTimestamp), ZoneId.systemDefault()));
            // 获取所需的K线周期
            List<String> timeframes = new ArrayList<>();
            timeframes.add(config.getMainTimeframe());
            if (!config.getMainTimeframe().equals(config.getSubTimeframe())) {
                timeframes.add(config.getSubTimeframe());
            }
            log.info("获取的K线周期为: {}", JSON.toJSONString(timeframes));

            // 为每个交易对抓取数据
            for (String symbol : symbols) {
                log.info("正在从交易所获取 {} 的历史数据", symbol);

                for (String timeframe : timeframes) {
                    // 将时间周期转换为OKX API格式
                    String okxBar = convertTimeframeToOkxFormat(timeframe);

                    // 按时间段分批获取数据
                    long step = getStepSize(timeframe);
                    List<KLineEntity> klines = new ArrayList<>();

                    for (long timestamp = startTimestamp; timestamp < endTimestamp; timestamp += step) {
                        // 计算结束时间点，不超过当前设置的结束时间
                        long batchEndTs = Math.min(timestamp + step, endTimestamp);
//                        log.info("正在获取 {} 的 {} 周期的数据，时间范围: {} - {}", symbol, timeframe,
//                            LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault()),
//                            LocalDateTime.ofInstant(Instant.ofEpochMilli(batchEndTs), ZoneId.systemDefault()));
                        // 调用OKX API获取K线数据
                        JSONObject barJson = marketDataAPIService.getCandlesticksHistory(
                            symbol,
                            String.valueOf(batchEndTs),
                            String.valueOf(timestamp),
                            okxBar,
                            "100"  // 每次最多获取300条数据
                        );

                        // 解析K线数据
                        List<KLineEntity> batchKlines = parseKlines(barJson, symbol, timeframe);
                        klines.addAll(batchKlines);

                        // 避免频繁请求触发限制
                        ThreadUtil.sleep(50);
                    }

                    // 添加到结果中
                    if (!klines.isEmpty()) {
                        // 按时间戳排序（降序）
                        klines.sort((k1, k2) -> Long.compare(k2.getTs(), k1.getTs()));

                        // 合并到历史数据中
                        List<KLineEntity> existingKlines = historicalData.computeIfAbsent(symbol, k -> new ArrayList<>());
                        existingKlines.addAll(klines);
                    }

                    log.info("{} {} 数据获取成功，共{}条", symbol, timeframe, klines.size());
                }
            }

        } catch (Exception e) {
            log.error("从交易所加载历史数据失败", e);
        }

        return historicalData;
    }

    /**
     * 解析K线数据
     */
    private static List<KLineEntity> parseKlines(JSONObject barJson, String symbol, String timeframe) {
        List<KLineEntity> klines = new ArrayList<>();

        if (barJson == null || !barJson.containsKey("data")) {
            return klines;
        }

        JSONArray dataArray = barJson.getJSONArray("data");
        if (dataArray == null) {
            return klines;
        }

        for (int i = 0; i < dataArray.size(); i++) {
            JSONArray item = dataArray.getJSONArray(i);
            if (item == null || item.size() < 8) {
                continue;
            }

            try {
                // 解析K线数据
                long ts = item.getLong(0);  // 开始时间，Unix时间戳的毫秒数格式
                String open = item.getString(1);  // 开盘价格
                String high = item.getString(2);  // 最高价格
                String low = item.getString(3);   // 最低价格
                String close = item.getString(4); // 收盘价格
                BigDecimal volCcy = item.getBigDecimal(7); // 交易量（按币种计算）

                // 创建KLineEntity对象
                KLineEntity kline = new KLineEntity();
                kline.setInstId(symbol);
                kline.setTs(ts / 1000); // 转换为秒
                kline.setOpen(new BigDecimal(open));
                kline.setHigh(new BigDecimal(high));
                kline.setLow(new BigDecimal(low));
                kline.setClose(new BigDecimal(close));
                kline.setVolume(volCcy != null ? volCcy.floatValue() : 0f);
                kline.setBar(timeframe);

                klines.add(kline);
            } catch (Exception e) {
                log.warn("解析K线数据失败: {}", e.getMessage());
            }
        }

        return klines;
    }

    /**
     * 获取交易对列表
     */
    private static List<String> getInstIds(PublicDataAPIService publicDataAPIService,
                                           MarketDataAPIService marketDataAPIService,
                                           int size) {
        Map<String, Double> volumeMap = new HashMap<>();
        JSONObject instruments = publicDataAPIService.getInstruments("SWAP", null, null);

        if (instruments == null || !instruments.containsKey("data")) {
            log.error("获取交易对列表失败");
            return new ArrayList<>();
        }

        final JSONArray jsonArray = instruments.getJSONArray("data");
        for (int i = 0; i < jsonArray.size(); i++) {
            final JSONObject object = jsonArray.getJSONObject(i);
            final String instId = object.getString("instId");
            if (!instId.contains("USDT")) {
                continue;
            }

            try {
                final double volume = getVolume(marketDataAPIService, instId);
                volumeMap.put(instId, volume);
            } catch (Exception e) {
                log.warn("获取{}交易量失败: {}", instId, e.getMessage());
            }

            ThreadUtil.sleep(100);
        }

        // 按交易量排序并返回前N个
        return volumeMap.entrySet().stream()
            .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
            .limit(size)
            .map(Map.Entry::getKey)
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取交易对的成交量
     */
    private static double getVolume(MarketDataAPIService marketDataAPIService, String instId) {
        JSONObject tickers = marketDataAPIService.getTicker(instId);
        if (tickers == null || !tickers.containsKey("data") || tickers.getJSONArray("data").isEmpty()) {
            return 0.0;
        }

        JSONObject data = tickers.getJSONArray("data").getJSONObject(0);
        final Double last = data.getDouble("last");
        final Double vol24h = data.getDouble("volCcy24h");
        return (last != null && vol24h != null) ? last * vol24h : 0.0;
    }

    /**
     * 根据K线周期计算时间步长（单位：毫秒）
     */
    private static long getStepSize(String timeframe) {
        // 将时间周期转换为毫秒数，再乘以300（每次获取300根K线）
        return getIntervalSeconds(timeframe) * 100L;
    }

    /**
     * 将回测系统的时间周期格式转换为OKX API格式
     */
    private static String convertTimeframeToOkxFormat(String timeframe) {
        // 1m, 5m, 15m -> 1m, 5m, 15m
        // 30m -> 30m
        // 1h, 4h -> 1H, 4H
        // 1d -> 1D
        if (timeframe.endsWith("h")) {
            return timeframe.substring(0, timeframe.length() - 1) + "H";
        } else if (timeframe.endsWith("d")) {
            return timeframe.substring(0, timeframe.length() - 1) + "D";
        }
        return timeframe;
    }

    /**
     * 创建模拟数据用于测试
     *
     * @param config 回测配置
     * @return 历史K线数据
     */
    public static Map<String, List<KLineEntity>> createMockData(BacktestConfig config) {
        Map<String, List<KLineEntity>> historicalData = new HashMap<>();

        // 默认交易对
        List<String> symbols = config.getSymbols();
        if (symbols == null || symbols.isEmpty()) {
            symbols = Arrays.asList("BTC-USDT-SWAP", "ETH-USDT-SWAP");
        }

        // 默认日期范围
        LocalDateTime startDate = config.getStartDate();
        LocalDateTime endDate = config.getEndDate();
        if (startDate == null) {
            startDate = LocalDateTime.now().minusDays(30);
        }
        if (endDate == null) {
            endDate = LocalDateTime.now();
        }

        // 生成模拟数据
        for (String symbol : symbols) {
            List<KLineEntity> mainTimeframeKlines = generateMockKlines(
                symbol, startDate, endDate, config.getMainTimeframe(), 100);
            List<KLineEntity> subTimeframeKlines = generateMockKlines(
                symbol, startDate, endDate, config.getSubTimeframe(), 2000);

            List<KLineEntity> combinedKlines = new ArrayList<>();
            combinedKlines.addAll(mainTimeframeKlines);
            combinedKlines.addAll(subTimeframeKlines);

            historicalData.put(symbol, combinedKlines);
        }

        log.info("生成了{}个交易对的模拟历史数据", historicalData.size());
        return historicalData;
    }

    /**
     * 生成模拟K线数据
     */
    private static List<KLineEntity> generateMockKlines(String symbol, LocalDateTime startDate,
                                                        LocalDateTime endDate, String timeframe,
                                                        int approxCount) {
        List<KLineEntity> klines = new ArrayList<>();

        // 确定K线间隔（秒）
        int intervalSeconds = getIntervalSeconds(timeframe);

        // 生成K线
        Random random = new Random(symbol.hashCode()); // 使用固定种子保证每次生成相同数据
        double basePrice = 100 + random.nextDouble() * 1000; // 基础价格
        double lastClose = basePrice;

        // 计算总K线数量和间隔
        long startEpoch = startDate.atZone(ZoneId.systemDefault()).toEpochSecond();
        long endEpoch = endDate.atZone(ZoneId.systemDefault()).toEpochSecond();
        long totalSeconds = endEpoch - startEpoch;
        int step = (int) (totalSeconds / approxCount);
        step = Math.max(step, intervalSeconds);

        // 生成K线
        for (long ts = startEpoch; ts <= endEpoch; ts += step) {
            // 生成价格波动，确保有一些趋势和随机性
            double range = lastClose * 0.02; // 2%波动范围
            double change = (random.nextDouble() - 0.5) * range;

            // 添加一些趋势
            if (random.nextDouble() > 0.7) {
                // 30%的概率有较强的趋势
                change += range * (random.nextDouble() - 0.3) * 2;
            }

            double open = lastClose;
            double close = open + change;

            // 确保价格为正
            close = Math.max(close, open * 0.5);

            // 确定高低点
            double max = Math.max(open, close);
            double min = Math.min(open, close);
            double high = max + random.nextDouble() * (max - min) * 0.5;
            double low = min - random.nextDouble() * (max - min) * 0.5;

            // 生成成交量
            double volume = lastClose * (10 + random.nextDouble() * 90);

            // 创建K线对象
            KLineEntity kline = new KLineEntity();
            kline.setInstId(symbol);
            kline.setTs(ts);
            kline.setOpen(BigDecimal.valueOf(open));
            kline.setHigh(BigDecimal.valueOf(high));
            kline.setLow(BigDecimal.valueOf(low));
            kline.setClose(BigDecimal.valueOf(close));
            kline.setVolume(new Double(volume).floatValue());
            kline.setBar(timeframe);

            klines.add(kline);
            lastClose = close;
        }

        // 排序（按时间降序）
        klines.sort((k1, k2) -> Long.compare(k2.getTs(), k1.getTs()));

        return klines;
    }

    /**
     * 将时间周期转换为毫秒数
     */
    private static int getIntervalSeconds(String timeframe) {
        // 例如：1m, 5m, 15m, 30m, 1h, 4h, 1d
        int multiplier = Integer.parseInt(timeframe.substring(0, timeframe.length() - 1));
        char unit = timeframe.charAt(timeframe.length() - 1);

        switch (unit) {
            case 'm':
                return multiplier * 60000;
            case 'h':
                return multiplier * 60 * 60000;
            case 'd':
                return multiplier * 24 * 60 * 60000;
            default:
                return 60; // 默认1分钟
        }
    }
}
