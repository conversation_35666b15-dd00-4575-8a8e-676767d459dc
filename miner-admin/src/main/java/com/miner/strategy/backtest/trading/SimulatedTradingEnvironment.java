package com.miner.strategy.backtest.trading;

import com.miner.strategy.backtest.core.event.Event;
import com.miner.strategy.backtest.core.event.EventBus;
import com.miner.strategy.backtest.core.event.EventHandler;
import com.miner.strategy.backtest.core.event.SignalEvent;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 模拟交易环境
 * 整合账户管理、订单执行、持仓管理、风险控制等功能
 *
 * <AUTHOR>
 */
@Slf4j
public class SimulatedTradingEnvironment implements EventHandler<SignalEvent> {

    // 核心组件
    private final Account account;
    private final OrderExecutionEngine orderEngine;
    private final PositionManager positionManager;
    private final RiskManager riskManager;
    private final EventBus eventBus;

    // 当前市场价格
    private final Map<String, BigDecimal> currentPrices = new ConcurrentHashMap<>();

    // 交易统计
    private final TradingStats tradingStats = new TradingStats();

    public SimulatedTradingEnvironment(BigDecimal initialBalance, EventBus eventBus) {
        this.eventBus = eventBus;

        // 初始化账户
        this.account = new Account(
            "BACKTEST_ACCOUNT",
            initialBalance,
            20, // 最大杠杆20倍
            BigDecimal.valueOf(0.25) // 最大回撤25%
        );

        // 初始化订单执行引擎
        OrderExecutionEngine.FeeConfig feeConfig = new OrderExecutionEngine.FeeConfig(
            BigDecimal.valueOf(0.0002), // Maker费率 0.02%
            BigDecimal.valueOf(0.0005)  // Taker费率 0.05%
        );
        OrderExecutionEngine.SlippageConfig slippageConfig = new OrderExecutionEngine.SlippageConfig(
            BigDecimal.valueOf(0.001),  // 市价单滑点 0.1%
            BigDecimal.valueOf(0.0)     // 限价单无滑点
        );
        this.orderEngine = new OrderExecutionEngine(feeConfig, slippageConfig);

        // 初始化持仓管理器
        this.positionManager = new PositionManager(account);

        // 初始化风险管理器
        RiskManager.RiskConfig riskConfig = new RiskManager.RiskConfig();
        this.riskManager = new RiskManager(riskConfig, account, positionManager);

        // 注册为信号事件处理器
        eventBus.register(this);

        log.info("模拟交易环境初始化完成: {}", account.getSummary());
    }

    @Override
    public void handle(SignalEvent event) {
        try {
            processSignal(event);
        } catch (Exception e) {
            log.error("处理交易信号时发生异常: {}", event, e);
        }
    }

    @Override
    public String getHandlerName() {
        return "SimulatedTradingEnvironment";
    }

    @Override
    public Event.EventType getSupportedEventType() {
        return Event.EventType.SIGNAL;
    }

    /**
     * 处理交易信号
     *
     * @param signal 交易信号
     */
    private void processSignal(SignalEvent signal) {
        String symbol = signal.getSymbol();
        BigDecimal currentPrice = currentPrices.get(symbol);

        if (currentPrice == null) {
            log.warn("交易对 {} 的市场价格不可用，忽略信号", symbol);
            return;
        }

        switch (signal.getSignalType()) {
            case BUY:
                handleBuySignal(signal, currentPrice);
                break;
            case SELL:
                handleSellSignal(signal, currentPrice);
                break;
            case CLOSE_LONG:
            case CLOSE_SHORT:
                handleCloseSignal(signal, currentPrice);
                break;
            case STOP_LOSS:
            case TAKE_PROFIT:
                handleStopSignal(signal, currentPrice);
                break;
            default:
                log.warn("未知的信号类型: {}", signal.getSignalType());
        }

        // 更新交易统计
        tradingStats.updateStats(signal);
    }

    /**
     * 处理买入信号
     *
     * @param signal       信号
     * @param currentPrice 当前价格
     */
    private void handleBuySignal(SignalEvent signal, BigDecimal currentPrice) {
        String symbol = signal.getSymbol();
        BigDecimal quantity = signal.getQuantity();
        int leverage = 10; // 默认杠杆10倍

        // 风险检查
        RiskManager.RiskCheckResult riskCheck = riskManager.checkOpenPositionRisk(
            symbol, Position.PositionSide.LONG, quantity, currentPrice, leverage);

        if (!riskCheck.isApproved()) {
            log.warn("买入信号风险检查失败: {} - {}", symbol, riskCheck.getMessage());
            tradingStats.rejectedSignals++;
            return;
        }

        // 创建市价买单
        Order order = Order.builder()
            .accountId(account.getAccountId())
            .symbol(symbol)
            .orderType(Order.OrderType.MARKET)
            .side(Order.OrderSide.BUY)
            .quantity(quantity)
            .leverage(leverage)
            .createTime(LocalDateTime.now())
            .build();

        // 提交订单
        String orderId = orderEngine.submitOrder(order);
        if (orderId != null) {
            // 开仓
            String positionId = positionManager.openPosition(symbol, Position.PositionSide.LONG,
                quantity, currentPrice, leverage);

            if (positionId != null) {
                log.info("买入成功: {} {} @ {}", symbol, quantity, currentPrice);
                tradingStats.successfulTrades++;
            } else {
                log.warn("开仓失败: {}", symbol);
                tradingStats.failedTrades++;
            }
        }
    }

    /**
     * 处理卖出信号
     *
     * @param signal       信号
     * @param currentPrice 当前价格
     */
    private void handleSellSignal(SignalEvent signal, BigDecimal currentPrice) {
        String symbol = signal.getSymbol();
        BigDecimal quantity = signal.getQuantity();
        int leverage = 10; // 默认杠杆10倍

        // 风险检查
        RiskManager.RiskCheckResult riskCheck = riskManager.checkOpenPositionRisk(
            symbol, Position.PositionSide.SHORT, quantity, currentPrice, leverage);

        if (!riskCheck.isApproved()) {
            log.warn("卖出信号风险检查失败: {} - {}", symbol, riskCheck.getMessage());
            tradingStats.rejectedSignals++;
            return;
        }

        // 创建市价卖单
        Order order = Order.builder()
            .accountId(account.getAccountId())
            .symbol(symbol)
            .orderType(Order.OrderType.MARKET)
            .side(Order.OrderSide.SELL)
            .quantity(quantity)
            .leverage(leverage)
            .createTime(LocalDateTime.now())
            .build();

        // 提交订单
        String orderId = orderEngine.submitOrder(order);
        if (orderId != null) {
            // 开仓
            String positionId = positionManager.openPosition(symbol, Position.PositionSide.SHORT,
                quantity, currentPrice, leverage);

            if (positionId != null) {
                log.info("卖出成功: {} {} @ {}", symbol, quantity, currentPrice);
                tradingStats.successfulTrades++;
            } else {
                log.warn("开仓失败: {}", symbol);
                tradingStats.failedTrades++;
            }
        }
    }

    /**
     * 处理平仓信号
     *
     * @param signal       信号
     * @param currentPrice 当前价格
     */
    private void handleCloseSignal(SignalEvent signal, BigDecimal currentPrice) {
        String symbol = signal.getSymbol();
        Position position = positionManager.getPosition(symbol);

        if (position == null) {
            log.warn("没有找到持仓: {}", symbol);
            return;
        }

        // 平仓
        BigDecimal realizedPnl = positionManager.closePosition(position.getPositionId(), currentPrice, BigDecimal.ZERO);

        log.info("平仓完成: {} 实现盈亏: {}", symbol, realizedPnl);
        tradingStats.successfulTrades++;

        if (realizedPnl.compareTo(BigDecimal.ZERO) > 0) {
            tradingStats.profitableTrades++;
        } else {
            tradingStats.losingTrades++;
        }
    }

    /**
     * 处理止损止盈信号
     *
     * @param signal       信号
     * @param currentPrice 当前价格
     */
    private void handleStopSignal(SignalEvent signal, BigDecimal currentPrice) {
        // 这里可以设置止损止盈价格
        // 暂时按平仓处理
        handleCloseSignal(signal, currentPrice);
    }

    /**
     * 更新市场价格
     *
     * @param symbol 交易对
     * @param price  价格
     */
    public void updateMarketPrice(String symbol, BigDecimal price) {
        currentPrices.put(symbol, price);

        // 更新订单执行引擎的市场价格
        orderEngine.updateMarketPrice(symbol, price);

        // 更新持仓价格
        positionManager.updatePositionPrice(symbol, price);

        // 更新风险统计
        riskManager.updateRiskStats(symbol, price);
    }

    /**
     * 获取账户信息
     *
     * @return 账户
     */
    public Account getAccount() {
        return account;
    }

    /**
     * 获取持仓管理器
     *
     * @return 持仓管理器
     */
    public PositionManager getPositionManager() {
        return positionManager;
    }

    /**
     * 获取风险管理器
     *
     * @return 风险管理器
     */
    public RiskManager getRiskManager() {
        return riskManager;
    }

    /**
     * 获取交易统计
     *
     * @return 交易统计
     */
    public TradingStats getTradingStats() {
        return tradingStats;
    }

    /**
     * 获取环境摘要
     *
     * @return 环境摘要
     */
    public String getEnvironmentSummary() {
        return String.format(
            "交易环境摘要:\n%s\n%s\n%s\n%s",
            account.getSummary(),
            riskManager.getRiskSummary(),
            tradingStats.getSummary(),
            "活跃持仓: " + positionManager.getActivePositionCount()
        );
    }

    /**
     * 交易统计
     */
    public static class TradingStats {
        public int totalSignals = 0;
        public int successfulTrades = 0;
        public int failedTrades = 0;
        public int rejectedSignals = 0;
        public int profitableTrades = 0;
        public int losingTrades = 0;

        public void updateStats(SignalEvent signal) {
            totalSignals++;
        }

        public double getSuccessRate() {
            return totalSignals > 0 ? (double) successfulTrades / totalSignals : 0.0;
        }

        public double getWinRate() {
            int totalClosedTrades = profitableTrades + losingTrades;
            return totalClosedTrades > 0 ? (double) profitableTrades / totalClosedTrades : 0.0;
        }

        public String getSummary() {
            return String.format(
                "交易统计: 总信号=%d, 成功=%d, 失败=%d, 拒绝=%d, 盈利=%d, 亏损=%d, 成功率=%.2f%%, 胜率=%.2f%%",
                totalSignals, successfulTrades, failedTrades, rejectedSignals,
                profitableTrades, losingTrades, getSuccessRate() * 100, getWinRate() * 100
            );
        }
    }
}
