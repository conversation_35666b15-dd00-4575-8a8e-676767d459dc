package com.miner.strategy.backtest.core;

import com.miner.strategy.backtest.core.impl.EMARetraceStrategyAdapter;
import com.miner.strategy.backtest.strategy.StrategyConfigManager;
import com.miner.strategy.backtest.strategy.StrategyMonitor;
import com.miner.strategy.backtest.strategy.SignalLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 策略注册器
 * 负责自动注册所有可用的策略
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class StrategyRegistry {
    
    @PostConstruct
    public void registerStrategies() {
        log.info("开始注册策略...");

        StrategyFactory factory = StrategyFactory.getInstance();

        // 注册EMA回调策略（增强版）
        factory.registerStrategy(
            "EMARetraceStrategy",
            EMARetraceStrategyAdapter::new,
            "EMA回调策略 - 基于EMA多头排列的完整回调入场策略，集成技术指标分析和信号评分系统",
            "2.1.0"
        );

        // 可以在这里注册更多策略
        // factory.registerStrategy("AnotherStrategy", AnotherStrategyAdapter::new, "描述", "版本");

        log.info("策略注册完成，共注册 {} 个策略", factory.getRegisteredStrategyCount());

        // 输出已注册的策略信息
        for (String strategyName : factory.getRegisteredStrategyNames()) {
            StrategyFactory.StrategyDescriptor descriptor = factory.getStrategyDescriptor(strategyName);
            log.info("已注册策略: {} - {} (版本: {})",
                    strategyName, descriptor.getDescription(), descriptor.getVersion());
        }

        // 初始化策略管理组件
        initializeStrategyManagement();
    }

    /**
     * 初始化策略管理组件
     */
    private void initializeStrategyManagement() {
        log.info("初始化策略管理组件...");

        // 初始化策略配置管理器
        StrategyConfigManager configManager = StrategyConfigManager.getInstance();
        log.info("策略配置管理器已初始化");

        // 初始化策略监控器
        StrategyMonitor monitor = StrategyMonitor.getInstance();
        log.info("策略监控器已初始化");

        // 初始化信号日志记录器
        SignalLogger signalLogger = SignalLogger.getInstance();
        log.info("信号日志记录器已初始化");

        log.info("策略管理组件初始化完成");
    }
}
