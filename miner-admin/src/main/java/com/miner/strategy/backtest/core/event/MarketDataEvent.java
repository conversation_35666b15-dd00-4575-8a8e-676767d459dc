package com.miner.strategy.backtest.core.event;

import com.miner.system.indicator.KLineEntity;

import java.util.Map;

/**
 * 市场数据事件
 * 包含K线数据和相关市场信息
 * 
 * <AUTHOR>
 */
public class MarketDataEvent extends Event {
    
    private final String symbol;
    private final KLineEntity klineData;
    private final String timeframe;
    private final Map<String, Object> additionalData;
    
    public MarketDataEvent(String symbol, KLineEntity klineData, String timeframe, 
                          Map<String, Object> additionalData) {
        super(EventType.MARKET_DATA, klineData.getTs());
        this.symbol = symbol;
        this.klineData = klineData;
        this.timeframe = timeframe;
        this.additionalData = additionalData;
    }
    
    public MarketDataEvent(String symbol, KLineEntity klineData, String timeframe) {
        this(symbol, klineData, timeframe, null);
    }
    
    public String getSymbol() {
        return symbol;
    }
    
    public KLineEntity getKlineData() {
        return klineData;
    }
    
    public String getTimeframe() {
        return timeframe;
    }
    
    public Map<String, Object> getAdditionalData() {
        return additionalData;
    }
    
    /**
     * 获取附加数据
     * 
     * @param key 键
     * @param <T> 值类型
     * @return 值
     */
    @SuppressWarnings("unchecked")
    public <T> T getAdditionalData(String key) {
        if (additionalData == null) {
            return null;
        }
        return (T) additionalData.get(key);
    }
    
    @Override
    public String toString() {
        return String.format("MarketDataEvent{symbol='%s', timeframe='%s', timestamp=%d, price=%s}", 
                           symbol, timeframe, getTimestamp(), 
                           klineData != null ? klineData.getClose() : "null");
    }
}
