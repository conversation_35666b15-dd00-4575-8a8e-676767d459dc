package com.miner.strategy.backtest.example;

import com.miner.strategy.backtest.config.BacktestConfig;
import com.miner.strategy.backtest.core.StrategyFactory;
import com.miner.strategy.backtest.core.TradingStrategy;
import com.miner.strategy.backtest.core.event.EventBus;
import com.miner.strategy.backtest.core.event.MarketDataEvent;
import com.miner.strategy.backtest.core.event.SignalEvent;
import com.miner.strategy.backtest.strategy.SignalLogger;
import com.miner.strategy.backtest.strategy.StrategyConfigManager;
import com.miner.strategy.backtest.strategy.StrategyMonitor;
import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 策略集成示例
 * 展示如何使用TASK-004实现的策略集成功能
 *
 * <AUTHOR>
 */
@Slf4j
public class StrategyIntegrationExample {

    public static void main(String[] args) {
        try {
            // 演示策略工厂使用
            demonstrateStrategyFactory();

            // 演示策略配置管理
            demonstrateStrategyConfigManagement();

            // 演示策略监控
            demonstrateStrategyMonitoring();

            // 演示信号日志记录
            demonstrateSignalLogging();

            // 演示完整的策略集成流程
            demonstrateCompleteIntegration();

        } catch (Exception e) {
            log.error("示例执行失败", e);
        }
    }

    /**
     * 演示策略工厂使用
     */
    public static void demonstrateStrategyFactory() {
        log.info("=== 策略工厂演示 ===");

        StrategyFactory factory = StrategyFactory.getInstance();

        // 显示已注册的策略
        log.info("已注册的策略:");
        for (String strategyName : factory.getRegisteredStrategyNames()) {
            StrategyFactory.StrategyDescriptor descriptor = factory.getStrategyDescriptor(strategyName);
            log.info("- {} (版本: {}) - {}",
                strategyName, descriptor.getVersion(), descriptor.getDescription());
        }

        // 创建策略实例
        BacktestConfig config = createSampleConfig();
        TradingStrategy strategy = factory.createStrategy("EMARetraceStrategy", config);

        log.info("创建策略实例: {}", strategy.getStrategyName());
        log.info("策略版本: {}", strategy.getVersion());
        log.info("策略状态: {}", strategy.getState());
        log.info("策略参数: {}", strategy.getParameters());

        log.info("==================");
    }

    /**
     * 演示策略配置管理
     */
    public static void demonstrateStrategyConfigManagement() {
        log.info("=== 策略配置管理演示 ===");

        StrategyConfigManager configManager = StrategyConfigManager.getInstance();

        // 从回测配置创建策略配置
        BacktestConfig backtestConfig = createSampleConfig();
        StrategyConfigManager.StrategyConfig strategyConfig =
            configManager.createFromBacktestConfig(backtestConfig);

        log.info("创建策略配置: {}", strategyConfig.getStrategyName());
        log.info("配置描述: {}", strategyConfig.getDescription());
        log.info("配置参数数量: {}", strategyConfig.getParameters().size());

        // 保存配置
        configManager.saveStrategyConfig("MyEMAStrategy", strategyConfig);
        log.info("配置已保存");

        // 更新参数
        Map<String, Object> newParams = new HashMap<>();
        newParams.put("emaFast", 12);
        newParams.put("entryScoreThreshold", 8.0);

        configManager.updateStrategyParameters("MyEMAStrategy", newParams);
        log.info("参数已更新: {}", newParams);

        // 获取更新后的配置
        StrategyConfigManager.StrategyConfig updatedConfig =
            configManager.getStrategyConfig("MyEMAStrategy");

        log.info("更新后的参数: emaFast={}, entryScoreThreshold={}",
            updatedConfig.getParameters().get("emaFast"),
            updatedConfig.getParameters().get("entryScoreThreshold"));

        log.info("==================");
    }

    /**
     * 演示策略监控
     */
    public static void demonstrateStrategyMonitoring() {
        log.info("=== 策略监控演示 ===");

        StrategyMonitor monitor = StrategyMonitor.getInstance();

        // 创建策略实例
        BacktestConfig config = createSampleConfig();
        TradingStrategy strategy = StrategyFactory.getInstance()
            .createStrategy("EMARetraceStrategy", config);

        // 注册策略监控
        monitor.registerStrategy(strategy);
        log.info("策略监控已注册");

        // 模拟策略运行和监控
        for (int i = 0; i < 5; i++) {
            // 更新策略状态
            monitor.updateStrategyState("EMARetraceStrategy", TradingStrategy.StrategyState.ACTIVE);

            // 记录信号生成
            monitor.recordSignalGenerated("EMARetraceStrategy", "BUY", 0.7 + Math.random() * 0.3);

            // 记录处理时间
            monitor.recordProcessingTime("EMARetraceStrategy", (long) (Math.random() * 50 + 10));

            try {
                Thread.sleep(100); // 模拟时间间隔
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        // 获取监控指标
        StrategyMonitor.StrategyMetrics metrics = monitor.getStrategyMetrics("EMARetraceStrategy");
        if (metrics != null) {
            log.info("策略指标:");
            log.info("  总信号数: {}", metrics.getTotalSignals());
            log.info("  交易信号数: {}", metrics.getTradingSignals());
            log.info("  平均置信度: {:.4f}", metrics.getAverageConfidence());
            log.info("  平均处理时间: {:.2f} ms", metrics.getAverageProcessingTime());
            log.info("  运行时间: {} 分钟", metrics.getUptime());
        }

        log.info("==================");
    }

    /**
     * 演示信号日志记录
     */
    public static void demonstrateSignalLogging() {
        log.info("=== 信号日志记录演示 ===");

        SignalLogger signalLogger = SignalLogger.getInstance();

        // 模拟生成多个信号
        for (int i = 0; i < 3; i++) {
            Map<String, Object> signalData = new HashMap<>();
            signalData.put("entryScore", 7.5 + Math.random() * 2);
            signalData.put("emaFast", 50000 + Math.random() * 1000);
            signalData.put("emaMid", 49500 + Math.random() * 1000);
            signalData.put("emaSlow", 49000 + Math.random() * 1000);
            signalData.put("rsi", 20 + Math.random() * 20);
            signalData.put("atr", 500 + Math.random() * 200);

            SignalEvent signal = new SignalEvent(
                "BTCUSDT",
                SignalEvent.SignalType.BUY,
                BigDecimal.valueOf(50000 + Math.random() * 1000),
                BigDecimal.valueOf(0.1),
                "EMARetraceStrategy",
                0.7 + Math.random() * 0.3,
                System.currentTimeMillis(),
                signalData
            );

            signalLogger.logSignal(signal);
        }

        // 获取统计信息
        SignalLogger.SignalStatistics stats = signalLogger.getStrategyStatistics("EMARetraceStrategy");
        if (stats != null) {
            log.info("信号统计:");
            log.info("  总信号数: {}", stats.getTotalSignals());
            log.info("  买入信号: {}", stats.getBuySignals());
            log.info("  平均置信度: {:.4f}", stats.getAverageConfidence());
            log.info("  平均评分: {:.2f}", stats.getAverageEntryScore());
            log.info("  最后信号时间: {}", stats.getLastSignalTime());
        }

        // 生成统计报告
        String report = signalLogger.generateStatisticsReport();
        log.info("信号统计报告:\n{}", report);

        log.info("==================");
    }

    /**
     * 演示完整的策略集成流程
     */
    public static void demonstrateCompleteIntegration() {
        log.info("=== 完整策略集成流程演示 ===");

        // 1. 创建事件总线
        EventBus eventBus = new EventBus();

        // 2. 创建策略实例
        BacktestConfig config = createSampleConfig();
        TradingStrategy strategy = StrategyFactory.getInstance()
            .createStrategy("EMARetraceStrategy", config);

        // 3. 注册监控和日志
        StrategyMonitor monitor = StrategyMonitor.getInstance();
        SignalLogger signalLogger = SignalLogger.getInstance();

        monitor.registerStrategy(strategy);

        // 4. 模拟市场数据处理
        log.info("开始模拟市场数据处理...");

        // 先提供足够的历史数据
        feedHistoricalData(strategy, "BTCUSDT", 60);

        // 模拟实时数据处理
        for (int i = 0; i < 10; i++) {
            long startTime = System.currentTimeMillis();

            // 创建市场数据
            KLineEntity kline = createTestKLine("BTCUSDT", 50000 + Math.random() * 2000,
                System.currentTimeMillis());
            MarketDataEvent marketEvent = new MarketDataEvent("BTCUSDT", kline, "4h");

            // 策略处理
            List<SignalEvent> signals = strategy.onMarketData(marketEvent);

            long processingTime = System.currentTimeMillis() - startTime;

            // 记录监控数据
            monitor.recordProcessingTime("EMARetraceStrategy", processingTime);

            // 处理生成的信号
            for (SignalEvent signal : signals) {
                // 记录信号
                signalLogger.logSignal(signal);
                monitor.recordSignalGenerated("EMARetraceStrategy",
                    signal.getSignalType().toString(),
                    signal.getConfidence());

                log.info("生成信号: {} {} @ {} 置信度: {:.2f}",
                    signal.getSymbol(), signal.getSignalType(),
                    signal.getPrice(), signal.getConfidence());
            }

            try {
                Thread.sleep(200); // 模拟时间间隔
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        // 5. 生成最终报告
        log.info("生成最终报告...");

        StrategyMonitor.StrategyMetrics metrics = monitor.getStrategyMetrics("EMARetraceStrategy");
        if (metrics != null) {
            log.info("策略性能报告:");
            log.info("  策略名称: {}", metrics.getStrategyName());
            log.info("  总信号数: {}", metrics.getTotalSignals());
            log.info("  平均置信度: {:.4f}", metrics.getAverageConfidence());
            log.info("  平均处理时间: {:.2f} ms", metrics.getAverageProcessingTime());
        }

        SignalLogger.SignalStatistics signalStats = signalLogger.getStrategyStatistics("EMARetraceStrategy");
        if (signalStats != null) {
            log.info("信号生成报告:");
            log.info("  买入信号: {}", signalStats.getBuySignals());
            log.info("  卖出信号: {}", signalStats.getSellSignals());
            log.info("  平均评分: {:.2f}", signalStats.getAverageEntryScore());
        }

        eventBus.shutdown();
        log.info("==================");
    }

    /**
     * 创建示例配置
     */
    private static BacktestConfig createSampleConfig() {
        return BacktestConfig.builder()
            .startDate(LocalDateTime.of(2024, 1, 1, 0, 0))
            .endDate(LocalDateTime.of(2024, 12, 31, 23, 59))
            .symbols(Arrays.asList("BTCUSDT", "ETHUSDT"))
            .initialCapital(10000.0)
            .positionSizeRatio(0.02)
            .mainTimeframe("4h")
            .subTimeframe("5m")
            .emaFast(9)
            .emaMid(21)
            .emaSlow(55)
            .rsiPeriod(14)
            .rsiOversold(30.0)
            .rsiRising(50.0)
            .entryScoreThreshold(7.0)
            .atrPeriod(14)
            .stopLossMultiplier(2.0)
            .takeProfitMultiplier1(10.0)
            .takeProfitMultiplier2(20.0)
            .tradeFeeRate(0.001)
            .slippageRatio(0.001)
            .build();
    }

    /**
     * 创建测试K线数据
     */
    private static KLineEntity createTestKLine(String symbol, double price, long timestamp) {
        KLineEntity kline = new KLineEntity();
        kline.setInstId(symbol);
        kline.setTs(timestamp);
        kline.setBar("4h");
        kline.setOpen(BigDecimal.valueOf(price * 0.99));
        kline.setHigh(BigDecimal.valueOf(price * 1.02));
        kline.setLow(BigDecimal.valueOf(price * 0.98));
        kline.setClose(BigDecimal.valueOf(price));
        kline.setVolume(BigDecimal.valueOf(1000).floatValue());
        return kline;
    }

    /**
     * 为策略提供历史数据
     */
    private static void feedHistoricalData(TradingStrategy strategy, String symbol, int count) {
        long baseTime = System.currentTimeMillis() - count * 4 * 60 * 60 * 1000L; // 4小时间隔

        for (int i = 0; i < count; i++) {
            double price = 50000.0 + Math.sin(i * 0.1) * 2000 + Math.random() * 500;
            KLineEntity kline = createTestKLine(symbol, price, baseTime + i * 4 * 60 * 60 * 1000L);
            MarketDataEvent event = new MarketDataEvent(symbol, kline, "4h");
            strategy.onMarketData(event);
        }
    }
}
