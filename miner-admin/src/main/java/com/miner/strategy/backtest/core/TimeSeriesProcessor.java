package com.miner.strategy.backtest.core;

import com.miner.system.indicator.KLineEntity;

import java.util.List;
import java.util.Map;

/**
 * 时间序列数据处理器接口
 * 负责处理历史数据的时间序列操作
 * 
 * <AUTHOR>
 */
public interface TimeSeriesProcessor {
    
    /**
     * 初始化处理器
     * 
     * @param historicalData 历史数据
     */
    void initialize(Map<String, List<KLineEntity>> historicalData);
    
    /**
     * 获取所有时间戳（按时间顺序）
     * 
     * @return 时间戳列表
     */
    List<Long> getAllTimestamps();
    
    /**
     * 获取指定时间戳的K线数据
     * 
     * @param symbol 交易对
     * @param timestamp 时间戳
     * @param timeframe 时间框架
     * @return K线数据
     */
    KLineEntity getKLineAt(String symbol, long timestamp, String timeframe);
    
    /**
     * 获取指定时间范围的K线数据
     * 
     * @param symbol 交易对
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param timeframe 时间框架
     * @return K线数据列表
     */
    List<KLineEntity> getKLineRange(String symbol, long startTime, long endTime, String timeframe);
    
    /**
     * 获取最新的N根K线
     * 
     * @param symbol 交易对
     * @param timestamp 当前时间戳
     * @param count 数量
     * @param timeframe 时间框架
     * @return K线数据列表
     */
    List<KLineEntity> getLatestKLines(String symbol, long timestamp, int count, String timeframe);
    
    /**
     * 检查数据完整性
     * 
     * @param symbol 交易对
     * @param timeframe 时间框架
     * @return 完整性检查结果
     */
    DataIntegrityResult checkDataIntegrity(String symbol, String timeframe);
    
    /**
     * 获取数据统计信息
     * 
     * @return 数据统计
     */
    DataStatistics getDataStatistics();
    
    /**
     * 数据完整性检查结果
     */
    class DataIntegrityResult {
        private final boolean complete;
        private final double completeness;
        private final List<Long> missingTimestamps;
        private final String message;
        
        public DataIntegrityResult(boolean complete, double completeness, 
                                 List<Long> missingTimestamps, String message) {
            this.complete = complete;
            this.completeness = completeness;
            this.missingTimestamps = missingTimestamps;
            this.message = message;
        }
        
        public boolean isComplete() {
            return complete;
        }
        
        public double getCompleteness() {
            return completeness;
        }
        
        public List<Long> getMissingTimestamps() {
            return missingTimestamps;
        }
        
        public String getMessage() {
            return message;
        }
    }
    
    /**
     * 数据统计信息
     */
    class DataStatistics {
        private final Map<String, Integer> symbolCounts;
        private final Map<String, Long> timeRanges;
        private final long totalDataPoints;
        private final double averageCompleteness;
        
        public DataStatistics(Map<String, Integer> symbolCounts, Map<String, Long> timeRanges,
                            long totalDataPoints, double averageCompleteness) {
            this.symbolCounts = symbolCounts;
            this.timeRanges = timeRanges;
            this.totalDataPoints = totalDataPoints;
            this.averageCompleteness = averageCompleteness;
        }
        
        public Map<String, Integer> getSymbolCounts() {
            return symbolCounts;
        }
        
        public Map<String, Long> getTimeRanges() {
            return timeRanges;
        }
        
        public long getTotalDataPoints() {
            return totalDataPoints;
        }
        
        public double getAverageCompleteness() {
            return averageCompleteness;
        }
    }
}
