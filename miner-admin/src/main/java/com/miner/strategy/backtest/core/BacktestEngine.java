package com.miner.strategy.backtest.core;

import com.miner.strategy.backtest.config.BacktestConfig;
import com.miner.strategy.backtest.model.BacktestResult;
import com.miner.system.indicator.KLineEntity;

import java.util.List;
import java.util.Map;

/**
 * 回测引擎接口
 * 定义回测引擎的核心功能
 * 
 * <AUTHOR>
 */
public interface BacktestEngine {
    
    /**
     * 初始化回测引擎
     * 
     * @param config 回测配置
     * @param historicalData 历史数据
     */
    void initialize(BacktestConfig config, Map<String, List<KLineEntity>> historicalData);
    
    /**
     * 运行回测
     * 
     * @return 回测结果
     */
    BacktestResult run();
    
    /**
     * 暂停回测
     */
    void pause();
    
    /**
     * 恢复回测
     */
    void resume();
    
    /**
     * 停止回测
     */
    void stop();
    
    /**
     * 获取回测状态
     * 
     * @return 回测状态
     */
    BacktestState getState();
    
    /**
     * 获取回测进度
     * 
     * @return 进度百分比 (0-100)
     */
    double getProgress();
    
    /**
     * 验证数据质量
     * 
     * @param data 历史数据
     * @return 验证结果
     */
    DataValidationResult validateData(Map<String, List<KLineEntity>> data);
    
    /**
     * 获取策略名称
     * 
     * @return 策略名称
     */
    String getStrategyName();
    
    /**
     * 回测状态枚举
     */
    enum BacktestState {
        INITIALIZED,    // 已初始化
        RUNNING,        // 运行中
        PAUSED,         // 已暂停
        STOPPED,        // 已停止
        COMPLETED,      // 已完成
        ERROR           // 错误状态
    }
    
    /**
     * 数据验证结果
     */
    class DataValidationResult {
        private final boolean valid;
        private final String message;
        private final double completeness; // 数据完整性百分比
        
        public DataValidationResult(boolean valid, String message, double completeness) {
            this.valid = valid;
            this.message = message;
            this.completeness = completeness;
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getMessage() {
            return message;
        }
        
        public double getCompleteness() {
            return completeness;
        }
    }
}
