package com.miner.strategy.backtest.core.event;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 事件基类
 * 所有事件的基础类
 * 
 * <AUTHOR>
 */
public abstract class Event {
    
    private final String eventId;
    private final EventType eventType;
    private final long timestamp;
    private final LocalDateTime eventTime;
    
    protected Event(EventType eventType, long timestamp) {
        this.eventId = UUID.randomUUID().toString();
        this.eventType = eventType;
        this.timestamp = timestamp;
        this.eventTime = LocalDateTime.now();
    }
    
    public String getEventId() {
        return eventId;
    }
    
    public EventType getEventType() {
        return eventType;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public LocalDateTime getEventTime() {
        return eventTime;
    }
    
    /**
     * 事件类型枚举
     */
    public enum EventType {
        MARKET_DATA,    // 市场数据事件
        SIGNAL,         // 信号事件
        ORDER,          // 订单事件
        FILL,           // 成交事件
        POSITION,       // 持仓事件
        RISK,           // 风险事件
        SYSTEM          // 系统事件
    }
    
    @Override
    public String toString() {
        return String.format("Event{id='%s', type=%s, timestamp=%d, time=%s}", 
                           eventId, eventType, timestamp, eventTime);
    }
}
