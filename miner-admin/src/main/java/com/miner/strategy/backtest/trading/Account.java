package com.miner.strategy.backtest.trading;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 模拟交易账户
 * 管理资金、余额、保证金等
 *
 * <AUTHOR>
 */
@Data
@Slf4j
public class Account {

    // 账户ID
    private final String accountId;

    // 初始资金
    private final BigDecimal initialBalance;

    // 当前可用余额
    private BigDecimal availableBalance;

    // 已用保证金
    private BigDecimal usedMargin;

    // 未实现盈亏
    private BigDecimal unrealizedPnl;

    // 已实现盈亏
    private BigDecimal realizedPnl;

    // 总权益 = 可用余额 + 已用保证金 + 未实现盈亏
    private BigDecimal totalEquity;

    // 最大杠杆倍数
    private final int maxLeverage;

    // 风险控制参数
    // 最大回撤比例
    private final BigDecimal maxDrawdownRatio;

    // 历史最高权益
    private BigDecimal maxEquity;

    // 交易统计
    private final Map<String, BigDecimal> symbolMargins = new ConcurrentHashMap<>();
    private final Map<String, BigDecimal> symbolPnl = new ConcurrentHashMap<>();

    // 账户创建时间
    private final LocalDateTime createTime;

    // 最后更新时间
    private LocalDateTime lastUpdateTime;

    public Account(String accountId, BigDecimal initialBalance, int maxLeverage, BigDecimal maxDrawdownRatio) {
        this.accountId = accountId;
        this.initialBalance = initialBalance;
        this.availableBalance = initialBalance;
        this.usedMargin = BigDecimal.ZERO;
        this.unrealizedPnl = BigDecimal.ZERO;
        this.realizedPnl = BigDecimal.ZERO;
        this.totalEquity = initialBalance;
        this.maxLeverage = maxLeverage;
        this.maxDrawdownRatio = maxDrawdownRatio;
        this.maxEquity = initialBalance;
        this.createTime = LocalDateTime.now();
        this.lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 冻结保证金
     *
     * @param symbol       交易对
     * @param marginAmount 保证金金额
     * @return 是否成功
     */
    public synchronized boolean freezeMargin(String symbol, BigDecimal marginAmount) {
        if (marginAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("保证金金额必须大于0: {}", marginAmount);
            return false;
        }

        if (availableBalance.compareTo(marginAmount) < 0) {
            log.warn("可用余额不足，无法冻结保证金。可用: {}, 需要: {}", availableBalance, marginAmount);
            return false;
        }

        // 检查杠杆限制
        BigDecimal newUsedMargin = usedMargin.add(marginAmount);
        BigDecimal maxAllowedMargin = totalEquity.multiply(BigDecimal.valueOf(maxLeverage));

        if (newUsedMargin.compareTo(maxAllowedMargin) > 0) {
            log.warn("超过最大杠杆限制。当前杠杆: {}x, 最大杠杆: {}x",
                newUsedMargin.divide(totalEquity, 2, RoundingMode.HALF_UP), maxLeverage);
            return false;
        }

        // 执行冻结
        availableBalance = availableBalance.subtract(marginAmount);
        usedMargin = usedMargin.add(marginAmount);
        symbolMargins.put(symbol, symbolMargins.getOrDefault(symbol, BigDecimal.ZERO).add(marginAmount));

        updateTotalEquity();
        lastUpdateTime = LocalDateTime.now();

        log.debug("冻结保证金成功: {} {}, 可用余额: {}", symbol, marginAmount, availableBalance);
        return true;
    }

    /**
     * 释放保证金
     *
     * @param symbol       交易对
     * @param marginAmount 保证金金额
     * @return 是否成功
     */
    public synchronized boolean releaseMargin(String symbol, BigDecimal marginAmount) {
        if (marginAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("保证金金额必须大于0: {}", marginAmount);
            return false;
        }

        BigDecimal symbolMargin = symbolMargins.getOrDefault(symbol, BigDecimal.ZERO);
        if (symbolMargin.compareTo(marginAmount) < 0) {
            log.warn("释放保证金金额超过已冻结金额。已冻结: {}, 释放: {}", symbolMargin, marginAmount);
            return false;
        }

        // 执行释放
        availableBalance = availableBalance.add(marginAmount);
        usedMargin = usedMargin.subtract(marginAmount);
        symbolMargins.put(symbol, symbolMargin.subtract(marginAmount));

        // 如果该交易对保证金为0，则移除记录
        if (symbolMargins.get(symbol).compareTo(BigDecimal.ZERO) == 0) {
            symbolMargins.remove(symbol);
        }

        updateTotalEquity();
        lastUpdateTime = LocalDateTime.now();

        log.debug("释放保证金成功: {} {}, 可用余额: {}", symbol, marginAmount, availableBalance);
        return true;
    }

    /**
     * 更新未实现盈亏
     *
     * @param symbol 交易对
     * @param pnl    盈亏金额
     */
    public synchronized void updateUnrealizedPnl(String symbol, BigDecimal pnl) {
        BigDecimal oldPnl = symbolPnl.getOrDefault(symbol, BigDecimal.ZERO);
        symbolPnl.put(symbol, pnl);

        // 重新计算总未实现盈亏
        unrealizedPnl = symbolPnl.values().stream()
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        updateTotalEquity();
        lastUpdateTime = LocalDateTime.now();

        log.debug("更新未实现盈亏: {} {} -> {}, 总未实现盈亏: {}",
            symbol, oldPnl, pnl, unrealizedPnl);
    }

    /**
     * 实现盈亏
     *
     * @param pnl 盈亏金额
     */
    public synchronized void realizePnl(BigDecimal pnl) {
        realizedPnl = realizedPnl.add(pnl);
        availableBalance = availableBalance.add(pnl);

        updateTotalEquity();
        lastUpdateTime = LocalDateTime.now();

        log.debug("实现盈亏: {}, 累计已实现盈亏: {}, 可用余额: {}",
            pnl, realizedPnl, availableBalance);
    }

    /**
     * 更新总权益
     */
    private void updateTotalEquity() {
        totalEquity = availableBalance.add(usedMargin).add(unrealizedPnl);

        // 更新历史最高权益
        if (totalEquity.compareTo(maxEquity) > 0) {
            maxEquity = totalEquity;
        }
    }

    /**
     * 计算当前回撤比例
     *
     * @return 回撤比例
     */
    public BigDecimal getCurrentDrawdownRatio() {
        if (maxEquity.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal drawdown = maxEquity.subtract(totalEquity);
        return drawdown.divide(maxEquity, 6, RoundingMode.HALF_UP);
    }

    /**
     * 检查是否触发风控
     *
     * @return 是否触发风控
     */
    public boolean isRiskControlTriggered() {
        BigDecimal currentDrawdownRatio = getCurrentDrawdownRatio();
        boolean triggered = currentDrawdownRatio.compareTo(maxDrawdownRatio) > 0;

        if (triggered) {
            log.warn("触发风控！当前回撤: {:.2f}%, 最大允许回撤: {:.2f}%",
                currentDrawdownRatio.multiply(BigDecimal.valueOf(100)),
                maxDrawdownRatio.multiply(BigDecimal.valueOf(100)));
        }

        return triggered;
    }

    /**
     * 计算当前杠杆倍数
     *
     * @return 杠杆倍数
     */
    public BigDecimal getCurrentLeverage() {
        if (totalEquity.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        return usedMargin.divide(totalEquity, 2, RoundingMode.HALF_UP);
    }

    /**
     * 获取账户摘要信息
     *
     * @return 账户摘要
     */
    public String getSummary() {
        return String.format(
            "账户[%s]: 总权益=%.2f, 可用=%.2f, 保证金=%.2f, 未实现盈亏=%.2f, 已实现盈亏=%.2f, 杠杆=%.2fx, 回撤=%.2f%%",
            accountId, totalEquity, availableBalance, usedMargin, unrealizedPnl, realizedPnl,
            getCurrentLeverage(), getCurrentDrawdownRatio().multiply(BigDecimal.valueOf(100))
        );
    }
}
