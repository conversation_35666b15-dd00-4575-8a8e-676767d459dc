package com.miner.strategy.backtest.strategy;

import com.miner.strategy.backtest.config.BacktestConfig;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 策略配置管理器
 * 负责策略参数的动态配置和管理
 * 
 * <AUTHOR>
 */
@Slf4j
public class StrategyConfigManager {
    
    private static final StrategyConfigManager INSTANCE = new StrategyConfigManager();
    
    // 策略配置缓存
    private final Map<String, StrategyConfig> configCache = new ConcurrentHashMap<>();
    
    // 配置文件路径
    private static final String CONFIG_DIR = "config/strategies/";
    
    private StrategyConfigManager() {
        // 初始化默认配置
        initializeDefaultConfigs();
    }
    
    public static StrategyConfigManager getInstance() {
        return INSTANCE;
    }
    
    /**
     * 获取策略配置
     * 
     * @param strategyName 策略名称
     * @return 策略配置
     */
    public StrategyConfig getStrategyConfig(String strategyName) {
        StrategyConfig config = configCache.get(strategyName);
        if (config == null) {
            config = loadStrategyConfig(strategyName);
            if (config != null) {
                configCache.put(strategyName, config);
            }
        }
        return config;
    }
    
    /**
     * 保存策略配置
     * 
     * @param strategyName 策略名称
     * @param config 策略配置
     */
    public void saveStrategyConfig(String strategyName, StrategyConfig config) {
        configCache.put(strategyName, config);
        saveConfigToFile(strategyName, config);
        log.info("保存策略配置: {} - {}", strategyName, config.getDescription());
    }
    
    /**
     * 更新策略参数
     * 
     * @param strategyName 策略名称
     * @param parameters 参数映射
     */
    public void updateStrategyParameters(String strategyName, Map<String, Object> parameters) {
        StrategyConfig config = getStrategyConfig(strategyName);
        if (config == null) {
            config = new StrategyConfig(strategyName);
        }
        
        config.getParameters().putAll(parameters);
        saveStrategyConfig(strategyName, config);
    }
    
    /**
     * 从BacktestConfig创建策略配置
     * 
     * @param backtestConfig 回测配置
     * @return 策略配置
     */
    public StrategyConfig createFromBacktestConfig(BacktestConfig backtestConfig) {
        StrategyConfig config = new StrategyConfig("EMARetraceStrategy");
        
        Map<String, Object> parameters = config.getParameters();
        parameters.put("emaFast", backtestConfig.getEmaFast());
        parameters.put("emaMid", backtestConfig.getEmaMid());
        parameters.put("emaSlow", backtestConfig.getEmaSlow());
        parameters.put("rsiPeriod", backtestConfig.getRsiPeriod());
        parameters.put("rsiOversold", backtestConfig.getRsiOversold());
        parameters.put("rsiRising", backtestConfig.getRsiRising());
        parameters.put("entryScoreThreshold", backtestConfig.getEntryScoreThreshold());
        parameters.put("atrPeriod", backtestConfig.getAtrPeriod());
        parameters.put("stopLossMultiplier", backtestConfig.getStopLossMultiplier());
        parameters.put("takeProfitMultiplier1", backtestConfig.getTakeProfitMultiplier1());
        parameters.put("takeProfitMultiplier2", backtestConfig.getTakeProfitMultiplier2());
        
        config.setDescription("从回测配置创建的EMA策略配置");
        config.setEnabled(true);
        
        return config;
    }
    
    /**
     * 初始化默认配置
     */
    private void initializeDefaultConfigs() {
        // EMA策略默认配置
        StrategyConfig emaConfig = new StrategyConfig("EMARetraceStrategy");
        emaConfig.setDescription("EMA回调策略默认配置");
        emaConfig.setEnabled(true);
        
        Map<String, Object> emaParams = emaConfig.getParameters();
        emaParams.put("emaFast", 9);
        emaParams.put("emaMid", 21);
        emaParams.put("emaSlow", 55);
        emaParams.put("rsiPeriod", 14);
        emaParams.put("rsiOversold", 30.0);
        emaParams.put("rsiRising", 50.0);
        emaParams.put("entryScoreThreshold", 7.0);
        emaParams.put("atrPeriod", 14);
        emaParams.put("stopLossMultiplier", 2.0);
        emaParams.put("takeProfitMultiplier1", 10.0);
        emaParams.put("takeProfitMultiplier2", 20.0);
        emaParams.put("retraceDepth", 0.8);
        
        configCache.put("EMARetraceStrategy", emaConfig);
        
        log.info("初始化默认策略配置完成");
    }
    
    /**
     * 从文件加载策略配置
     * 
     * @param strategyName 策略名称
     * @return 策略配置
     */
    private StrategyConfig loadStrategyConfig(String strategyName) {
        String configFile = CONFIG_DIR + strategyName + ".properties";
        File file = new File(configFile);
        
        if (!file.exists()) {
            log.debug("策略配置文件不存在: {}", configFile);
            return null;
        }
        
        try (InputStream input = new FileInputStream(file)) {
            Properties props = new Properties();
            props.load(input);
            
            StrategyConfig config = new StrategyConfig(strategyName);
            config.setDescription(props.getProperty("description", ""));
            config.setEnabled(Boolean.parseBoolean(props.getProperty("enabled", "true")));
            
            // 加载参数
            Map<String, Object> parameters = config.getParameters();
            for (String key : props.stringPropertyNames()) {
                if (!key.equals("description") && !key.equals("enabled")) {
                    String value = props.getProperty(key);
                    parameters.put(key, parseParameterValue(value));
                }
            }
            
            log.info("加载策略配置: {} - {}", strategyName, config.getDescription());
            return config;
            
        } catch (IOException e) {
            log.error("加载策略配置失败: {}", configFile, e);
            return null;
        }
    }
    
    /**
     * 保存配置到文件
     * 
     * @param strategyName 策略名称
     * @param config 策略配置
     */
    private void saveConfigToFile(String strategyName, StrategyConfig config) {
        String configFile = CONFIG_DIR + strategyName + ".properties";
        File file = new File(configFile);
        
        // 创建目录
        file.getParentFile().mkdirs();
        
        try (OutputStream output = new FileOutputStream(file)) {
            Properties props = new Properties();
            
            props.setProperty("description", config.getDescription());
            props.setProperty("enabled", String.valueOf(config.isEnabled()));
            
            // 保存参数
            for (Map.Entry<String, Object> entry : config.getParameters().entrySet()) {
                props.setProperty(entry.getKey(), entry.getValue().toString());
            }
            
            props.store(output, "Strategy Configuration for " + strategyName);
            log.debug("保存策略配置到文件: {}", configFile);
            
        } catch (IOException e) {
            log.error("保存策略配置失败: {}", configFile, e);
        }
    }
    
    /**
     * 解析参数值
     * 
     * @param value 字符串值
     * @return 解析后的对象
     */
    private Object parseParameterValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return value;
        }
        
        // 尝试解析为数字
        try {
            if (value.contains(".")) {
                return Double.parseDouble(value);
            } else {
                return Integer.parseInt(value);
            }
        } catch (NumberFormatException e) {
            // 尝试解析为布尔值
            if ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value)) {
                return Boolean.parseBoolean(value);
            }
            
            // 返回字符串
            return value;
        }
    }
    
    /**
     * 获取所有策略配置
     * 
     * @return 策略配置映射
     */
    public Map<String, StrategyConfig> getAllConfigs() {
        return new HashMap<>(configCache);
    }
    
    /**
     * 清除配置缓存
     */
    public void clearCache() {
        configCache.clear();
        log.info("清除策略配置缓存");
    }
    
    /**
     * 策略配置类
     */
    public static class StrategyConfig {
        private final String strategyName;
        private String description = "";
        private boolean enabled = true;
        private final Map<String, Object> parameters = new HashMap<>();
        
        public StrategyConfig(String strategyName) {
            this.strategyName = strategyName;
        }
        
        public String getStrategyName() {
            return strategyName;
        }
        
        public String getDescription() {
            return description;
        }
        
        public void setDescription(String description) {
            this.description = description;
        }
        
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public Map<String, Object> getParameters() {
            return parameters;
        }
        
        @Override
        public String toString() {
            return String.format("StrategyConfig{name='%s', enabled=%s, params=%d}", 
                               strategyName, enabled, parameters.size());
        }
    }
}
