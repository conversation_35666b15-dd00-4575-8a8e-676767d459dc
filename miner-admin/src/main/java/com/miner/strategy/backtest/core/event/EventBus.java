package com.miner.strategy.backtest.core.event;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 事件总线
 * 负责事件的发布和订阅管理
 * 
 * <AUTHOR>
 */
@Slf4j
public class EventBus {
    
    private final Map<Event.EventType, List<EventHandler<? extends Event>>> handlers;
    private final ExecutorService executorService;
    private final boolean asyncMode;
    
    public EventBus() {
        this(false);
    }
    
    public EventBus(boolean asyncMode) {
        this.handlers = new ConcurrentHashMap<>();
        this.asyncMode = asyncMode;
        this.executorService = asyncMode ? Executors.newCachedThreadPool() : null;
        
        // 初始化所有事件类型的处理器列表
        for (Event.EventType eventType : Event.EventType.values()) {
            handlers.put(eventType, new CopyOnWriteArrayList<>());
        }
    }
    
    /**
     * 注册事件处理器
     * 
     * @param handler 事件处理器
     */
    public void register(EventHandler<? extends Event> handler) {
        Event.EventType eventType = handler.getSupportedEventType();
        handlers.get(eventType).add(handler);
        log.debug("注册事件处理器: {} for {}", handler.getHandlerName(), eventType);
    }
    
    /**
     * 取消注册事件处理器
     * 
     * @param handler 事件处理器
     */
    public void unregister(EventHandler<? extends Event> handler) {
        Event.EventType eventType = handler.getSupportedEventType();
        handlers.get(eventType).remove(handler);
        log.debug("取消注册事件处理器: {} for {}", handler.getHandlerName(), eventType);
    }
    
    /**
     * 发布事件
     * 
     * @param event 事件
     */
    public void publish(Event event) {
        List<EventHandler<? extends Event>> eventHandlers = handlers.get(event.getEventType());
        
        if (eventHandlers.isEmpty()) {
            log.debug("没有找到事件处理器: {}", event.getEventType());
            return;
        }
        
        if (asyncMode) {
            // 异步处理
            executorService.submit(() -> processEvent(event, eventHandlers));
        } else {
            // 同步处理
            processEvent(event, eventHandlers);
        }
    }
    
    /**
     * 处理事件
     * 
     * @param event 事件
     * @param eventHandlers 事件处理器列表
     */
    @SuppressWarnings("unchecked")
    private void processEvent(Event event, List<EventHandler<? extends Event>> eventHandlers) {
        for (EventHandler<? extends Event> handler : eventHandlers) {
            try {
                if (handler.canHandle(event)) {
                    ((EventHandler<Event>) handler).handle(event);
                }
            } catch (Exception e) {
                log.error("事件处理器 {} 处理事件 {} 时发生异常: {}", 
                         handler.getHandlerName(), event.getEventId(), e.getMessage(), e);
            }
        }
    }
    
    /**
     * 获取指定事件类型的处理器数量
     * 
     * @param eventType 事件类型
     * @return 处理器数量
     */
    public int getHandlerCount(Event.EventType eventType) {
        return handlers.get(eventType).size();
    }
    
    /**
     * 获取总的处理器数量
     * 
     * @return 总处理器数量
     */
    public int getTotalHandlerCount() {
        return handlers.values().stream().mapToInt(List::size).sum();
    }
    
    /**
     * 清空所有处理器
     */
    public void clear() {
        handlers.values().forEach(List::clear);
        log.info("已清空所有事件处理器");
    }
    
    /**
     * 关闭事件总线
     */
    public void shutdown() {
        if (executorService != null) {
            executorService.shutdown();
        }
        clear();
        log.info("事件总线已关闭");
    }
}
