package com.miner.strategy.backtest.core;

import com.miner.strategy.backtest.config.BacktestConfig;
import com.miner.strategy.backtest.core.event.MarketDataEvent;
import com.miner.strategy.backtest.core.event.SignalEvent;
import com.miner.system.indicator.KLineEntity;

import java.util.List;
import java.util.Map;

/**
 * 交易策略接口
 * 定义策略的核心功能
 * 
 * <AUTHOR>
 */
public interface TradingStrategy {
    
    /**
     * 初始化策略
     * 
     * @param config 回测配置
     */
    void initialize(BacktestConfig config);
    
    /**
     * 处理市场数据事件
     * 
     * @param event 市场数据事件
     * @return 生成的信号事件列表
     */
    List<SignalEvent> onMarketData(MarketDataEvent event);
    
    /**
     * 获取策略名称
     * 
     * @return 策略名称
     */
    String getStrategyName();
    
    /**
     * 获取策略版本
     * 
     * @return 策略版本
     */
    String getVersion();
    
    /**
     * 获取策略描述
     * 
     * @return 策略描述
     */
    String getDescription();
    
    /**
     * 获取策略参数
     * 
     * @return 参数映射
     */
    Map<String, Object> getParameters();
    
    /**
     * 设置策略参数
     * 
     * @param parameters 参数映射
     */
    void setParameters(Map<String, Object> parameters);
    
    /**
     * 验证策略参数
     * 
     * @param parameters 参数映射
     * @return 验证结果
     */
    ParameterValidationResult validateParameters(Map<String, Object> parameters);
    
    /**
     * 重置策略状态
     */
    void reset();
    
    /**
     * 获取策略状态
     * 
     * @return 策略状态
     */
    StrategyState getState();
    
    /**
     * 策略状态枚举
     */
    enum StrategyState {
        INITIALIZED,    // 已初始化
        ACTIVE,         // 活跃状态
        PAUSED,         // 暂停状态
        STOPPED         // 停止状态
    }
    
    /**
     * 参数验证结果
     */
    class ParameterValidationResult {
        private final boolean valid;
        private final String message;
        private final List<String> errors;
        
        public ParameterValidationResult(boolean valid, String message, List<String> errors) {
            this.valid = valid;
            this.message = message;
            this.errors = errors;
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getMessage() {
            return message;
        }
        
        public List<String> getErrors() {
            return errors;
        }
    }
}
