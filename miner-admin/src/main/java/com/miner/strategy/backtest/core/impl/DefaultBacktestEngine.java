package com.miner.strategy.backtest.core.impl;

import com.miner.strategy.backtest.config.BacktestConfig;
import com.miner.strategy.backtest.core.BacktestEngine;
import com.miner.strategy.backtest.core.StrategyExecutor;
import com.miner.strategy.backtest.core.StrategyFactory;
import com.miner.strategy.backtest.core.TimeSeriesProcessor;
import com.miner.strategy.backtest.core.TradingStrategy;
import com.miner.strategy.backtest.core.event.Event;
import com.miner.strategy.backtest.core.event.EventBus;
import com.miner.strategy.backtest.core.event.EventHandler;
import com.miner.strategy.backtest.core.event.MarketDataEvent;
import com.miner.strategy.backtest.core.event.SignalEvent;
import com.miner.strategy.backtest.model.BacktestResult;
import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 默认回测引擎实现
 * 实现了完整的事件驱动回测框架
 *
 * <AUTHOR>
 */
@Slf4j
public class DefaultBacktestEngine implements BacktestEngine {

    private BacktestConfig config;
    private TimeSeriesProcessor timeSeriesProcessor;
    private StrategyExecutor strategyExecutor;
    private EventBus eventBus;
    private TradingStrategy strategy;

    private final AtomicReference<BacktestState> state = new AtomicReference<>(BacktestState.INITIALIZED);
    private final AtomicInteger processedTimestamps = new AtomicInteger(0);
    private int totalTimestamps = 0;

    // 回测结果收集
    private BacktestResultCollector resultCollector;

    public DefaultBacktestEngine() {
        // 同步模式以保证回测的确定性
        this.eventBus = new EventBus(false);
        this.timeSeriesProcessor = new DefaultTimeSeriesProcessor();
        // 同步执行
        this.strategyExecutor = new StrategyExecutor(eventBus, false);
        this.resultCollector = new BacktestResultCollector();

        // 注册结果收集器作为事件处理器
        eventBus.register(resultCollector);
    }

    @Override
    public void initialize(BacktestConfig config, Map<String, List<KLineEntity>> historicalData) {
        this.config = config;

        log.info("初始化回测引擎...");

        // 验证数据
        DataValidationResult validation = validateData(historicalData);
        if (!validation.isValid()) {
            throw new IllegalArgumentException("数据验证失败: " + validation.getMessage());
        }

        // 初始化时间序列处理器
        timeSeriesProcessor.initialize(historicalData);

        // 创建策略实例
        String strategyName = determineStrategyName(config);
        this.strategy = StrategyFactory.getInstance().createStrategy(strategyName, config);

        // 注册策略到执行器
        strategyExecutor.registerStrategy(strategy);

        // 初始化结果收集器
        resultCollector.initialize(config);

        // 获取总时间戳数量用于进度计算
        this.totalTimestamps = timeSeriesProcessor.getAllTimestamps().size();

        state.set(BacktestState.INITIALIZED);
        log.info("回测引擎初始化完成，策略: {}, 数据点: {}", strategyName, totalTimestamps);
    }

    @Override
    public BacktestResult run() {
        if (state.get() != BacktestState.INITIALIZED) {
            throw new IllegalStateException("回测引擎未正确初始化");
        }

        state.set(BacktestState.RUNNING);
        log.info("开始执行回测...");

        try {
            List<Long> timestamps = timeSeriesProcessor.getAllTimestamps();
            processedTimestamps.set(0);

            for (long timestamp : timestamps) {
                if (state.get() == BacktestState.STOPPED) {
                    log.info("回测被停止");
                    break;
                }

                if (state.get() == BacktestState.PAUSED) {
                    // 等待恢复
                    waitForResume();
                }

                processTimestamp(timestamp);
                processedTimestamps.incrementAndGet();

                // 定期输出进度
                if (processedTimestamps.get() % 1000 == 0) {
                    log.debug("回测进度: {:.1f}%", getProgress());
                }
            }

            state.set(BacktestState.COMPLETED);
            log.info("回测执行完成");

            // 生成回测结果
            return generateResult();

        } catch (Exception e) {
            state.set(BacktestState.ERROR);
            log.error("回测执行过程中发生异常", e);
            throw new RuntimeException("回测执行失败", e);
        }
    }

    @Override
    public void pause() {
        if (state.get() == BacktestState.RUNNING) {
            state.set(BacktestState.PAUSED);
            log.info("回测已暂停");
        }
    }

    @Override
    public void resume() {
        if (state.get() == BacktestState.PAUSED) {
            state.set(BacktestState.RUNNING);
            log.info("回测已恢复");
        }
    }

    @Override
    public void stop() {
        BacktestState currentState = state.get();
        if (currentState == BacktestState.RUNNING || currentState == BacktestState.PAUSED) {
            state.set(BacktestState.STOPPED);
            log.info("回测已停止");
        }
    }

    @Override
    public BacktestState getState() {
        return state.get();
    }

    @Override
    public double getProgress() {
        if (totalTimestamps == 0) {
            return 0.0;
        }
        return (double) processedTimestamps.get() / totalTimestamps * 100.0;
    }

    @Override
    public DataValidationResult validateData(Map<String, List<KLineEntity>> data) {
        if (data == null || data.isEmpty()) {
            return new DataValidationResult(false, "历史数据为空", 0.0);
        }

        int totalExpected = 0;
        int totalActual = 0;
        StringBuilder issues = new StringBuilder();

        for (Map.Entry<String, List<KLineEntity>> entry : data.entrySet()) {
            String key = entry.getKey();
            List<KLineEntity> klines = entry.getValue();

            if (klines == null || klines.isEmpty()) {
                issues.append(String.format("交易对 %s 数据为空; ", key));
                continue;
            }

            // 检查数据排序
            boolean sorted = true;
            for (int i = 1; i < klines.size(); i++) {
                if (klines.get(i).getTs() < klines.get(i - 1).getTs()) {
                    sorted = false;
                    break;
                }
            }

            if (!sorted) {
                issues.append(String.format("交易对 %s 数据未按时间排序; ", key));
            }

            totalActual += klines.size();
            totalExpected += klines.size(); // 简化计算
        }

        double completeness = totalExpected > 0 ? (double) totalActual / totalExpected : 0.0;
        boolean valid = issues.length() == 0 && completeness > 0.99;

        String message = valid ? "数据验证通过" : issues.toString();

        return new DataValidationResult(valid, message, completeness);
    }

    @Override
    public String getStrategyName() {
        return strategy != null ? strategy.getStrategyName() : "Unknown";
    }

    /**
     * 处理单个时间戳
     */
    private void processTimestamp(long timestamp) {
        // 为每个交易对生成市场数据事件
        for (String symbol : config.getSymbols()) {
            KLineEntity kline = timeSeriesProcessor.getKLineAt(symbol, timestamp, config.getMainTimeframe());
            if (kline != null) {
                MarketDataEvent event = new MarketDataEvent(symbol, kline, config.getMainTimeframe());

                // 发布市场数据事件
                eventBus.publish(event);

                // 执行策略
                strategyExecutor.executeStrategies(event);
            }
        }
    }

    /**
     * 等待恢复
     */
    private void waitForResume() {
        while (state.get() == BacktestState.PAUSED) {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    /**
     * 确定策略名称
     */
    private String determineStrategyName(BacktestConfig config) {
        // 这里可以根据配置参数自动确定策略类型
        // 暂时返回默认策略名称
        return "EMARetraceStrategy";
    }

    /**
     * 生成回测结果
     */
    private BacktestResult generateResult() {
        return resultCollector.generateResult(strategy.getStrategyName());
    }

    /**
     * 回测结果收集器
     */
    private class BacktestResultCollector implements EventHandler<SignalEvent> {

        private BacktestConfig config;
        // 这里可以添加更多的结果收集逻辑

        public void initialize(BacktestConfig config) {
            this.config = config;
        }

        @Override
        public void handle(SignalEvent event) {
            // 处理信号事件，收集交易数据
            log.debug("收集信号事件: {}", event);
        }

        @Override
        public String getHandlerName() {
            return "BacktestResultCollector";
        }

        @Override
        public Event.EventType getSupportedEventType() {
            return Event.EventType.SIGNAL;
        }

        public BacktestResult generateResult(String strategyName) {
            // 这里应该基于收集的数据生成完整的回测结果
            // 暂时返回一个简单的结果
            return BacktestResult.builder()
                .strategyName(strategyName)
                .startDate(config.getStartDate())
                .endDate(config.getEndDate())
                .symbols(config.getSymbols())
                .initialCapital(config.getInitialCapital())
                .finalCapital(config.getInitialCapital()) // 暂时设为相同
                .build();
        }
    }
}
