package com.miner.strategy.backtest.trading;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 订单执行引擎
 * 负责订单的执行、撮合、滑点和手续费计算
 *
 * <AUTHOR>
 */
@Slf4j
public class OrderExecutionEngine {

    // 订单ID生成器
    private final AtomicLong orderIdGenerator = new AtomicLong(1);

    // 待处理订单
    private final Map<String, Order> pendingOrders = new ConcurrentHashMap<>();

    // 已完成订单
    private final Map<String, Order> completedOrders = new ConcurrentHashMap<>();

    // 当前市场价格
    private final Map<String, BigDecimal> marketPrices = new ConcurrentHashMap<>();

    // 手续费配置
    private final FeeConfig feeConfig;

    // 滑点配置
    private final SlippageConfig slippageConfig;

    public OrderExecutionEngine(FeeConfig feeConfig, SlippageConfig slippageConfig) {
        this.feeConfig = feeConfig;
        this.slippageConfig = slippageConfig;
    }

    /**
     * 提交订单
     *
     * @param order 订单
     * @return 订单ID
     */
    public String submitOrder(Order order) {
        // 生成订单ID
        String orderId = "ORDER_" + orderIdGenerator.getAndIncrement();
        order.setOrderId(orderId);
        order.setStatus(Order.OrderStatus.PENDING);
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());
        order.setFilledQuantity(BigDecimal.ZERO);

        // 设置手续费率
        order.setFeeRate(calculateFeeRate(order));

        pendingOrders.put(orderId, order);

        log.debug("订单已提交: {}", order);

        // 如果是市价单，立即执行
        if (order.isMarketOrder()) {
            executeMarketOrder(order);
        }

        return orderId;
    }

    /**
     * 取消订单
     *
     * @param orderId 订单ID
     * @param reason  取消原因
     * @return 是否成功
     */
    public boolean cancelOrder(String orderId, String reason) {
        Order order = pendingOrders.get(orderId);
        if (order == null) {
            log.warn("订单不存在或已完成: {}", orderId);
            return false;
        }

        if (!order.isCancellable()) {
            log.warn("订单状态不允许取消: {}", order);
            return false;
        }

        order.cancel(reason);
        moveToCompleted(order);

        log.info("订单已取消: {} - {}", orderId, reason);
        return true;
    }

    /**
     * 更新市场价格并检查限价单
     *
     * @param symbol 交易对
     * @param price  价格
     */
    public void updateMarketPrice(String symbol, BigDecimal price) {
        marketPrices.put(symbol, price);

        // 检查限价单是否可以执行
        checkLimitOrders(symbol, price);
    }

    /**
     * 执行市价单
     *
     * @param order 订单
     */
    private void executeMarketOrder(Order order) {
        BigDecimal marketPrice = marketPrices.get(order.getSymbol());
        if (marketPrice == null) {
            order.reject("市场价格不可用");
            moveToCompleted(order);
            return;
        }

        // 计算滑点后的执行价格
        BigDecimal executionPrice = calculateExecutionPrice(order, marketPrice);

        // 计算手续费
        BigDecimal fee = calculateFee(order, executionPrice);

        // 执行订单
        executeOrder(order, executionPrice, fee);
    }

    /**
     * 检查限价单
     *
     * @param symbol       交易对
     * @param currentPrice 当前价格
     */
    private void checkLimitOrders(String symbol, BigDecimal currentPrice) {
        pendingOrders.values().stream()
            .filter(order -> order.getSymbol().equals(symbol))
            .filter(Order::isLimitOrder)
            .filter(order -> canExecuteLimitOrder(order, currentPrice))
            .forEach(order -> {
                BigDecimal executionPrice = order.getPrice();
                BigDecimal fee = calculateFee(order, executionPrice);
                executeOrder(order, executionPrice, fee);
            });
    }

    /**
     * 检查限价单是否可以执行
     *
     * @param order        订单
     * @param currentPrice 当前价格
     * @return 是否可以执行
     */
    private boolean canExecuteLimitOrder(Order order, BigDecimal currentPrice) {
        if (order.isBuyOrder()) {
            // 买单：当前价格 <= 限价
            return currentPrice.compareTo(order.getPrice()) <= 0;
        } else {
            // 卖单：当前价格 >= 限价
            return currentPrice.compareTo(order.getPrice()) >= 0;
        }
    }

    /**
     * 执行订单
     *
     * @param order          订单
     * @param executionPrice 执行价格
     * @param fee            手续费
     */
    private void executeOrder(Order order, BigDecimal executionPrice, BigDecimal fee) {
        order.updateFill(order.getQuantity(), executionPrice);
        order.setFee(fee);
        order.setSlippage(calculateSlippage(order, executionPrice));

        moveToCompleted(order);

        log.info("订单执行完成: {} @ {}, 手续费: {}",
            order.getOrderId(), executionPrice, fee);
    }

    /**
     * 计算执行价格（包含滑点）
     *
     * @param order       订单
     * @param marketPrice 市场价格
     * @return 执行价格
     */
    private BigDecimal calculateExecutionPrice(Order order, BigDecimal marketPrice) {
        BigDecimal slippageRate = slippageConfig.getSlippageRate(order.getOrderType());

        if (order.isBuyOrder()) {
            // 买单：价格上滑
            return marketPrice.multiply(BigDecimal.ONE.add(slippageRate));
        } else {
            // 卖单：价格下滑
            return marketPrice.multiply(BigDecimal.ONE.subtract(slippageRate));
        }
    }

    /**
     * 计算手续费
     *
     * @param order          订单
     * @param executionPrice 执行价格
     * @return 手续费
     */
    private BigDecimal calculateFee(Order order, BigDecimal executionPrice) {
        BigDecimal feeRate = order.getFeeRate();
        BigDecimal orderValue = order.getQuantity().multiply(executionPrice);
        return orderValue.multiply(feeRate);
    }

    /**
     * 计算手续费率
     *
     * @param order 订单
     * @return 手续费率
     */
    private BigDecimal calculateFeeRate(Order order) {
        if (order.isMarketOrder()) {
            return feeConfig.getTakerFeeRate();
        } else {
            return feeConfig.getMakerFeeRate();
        }
    }

    /**
     * 计算滑点
     *
     * @param order          订单
     * @param executionPrice 执行价格
     * @return 滑点金额
     */
    private BigDecimal calculateSlippage(Order order, BigDecimal executionPrice) {
        if (order.isLimitOrder()) {
            return BigDecimal.ZERO; // 限价单无滑点
        }

        BigDecimal marketPrice = marketPrices.get(order.getSymbol());
        if (marketPrice == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal slippageAmount = executionPrice.subtract(marketPrice).abs();
        return slippageAmount.multiply(order.getQuantity());
    }

    /**
     * 将订单移动到已完成列表
     *
     * @param order 订单
     */
    private void moveToCompleted(Order order) {
        pendingOrders.remove(order.getOrderId());
        completedOrders.put(order.getOrderId(), order);
    }

    /**
     * 获取订单
     *
     * @param orderId 订单ID
     * @return 订单
     */
    public Order getOrder(String orderId) {
        Order order = pendingOrders.get(orderId);
        if (order == null) {
            order = completedOrders.get(orderId);
        }
        return order;
    }

    /**
     * 获取待处理订单数量
     *
     * @return 待处理订单数量
     */
    public int getPendingOrderCount() {
        return pendingOrders.size();
    }

    /**
     * 获取已完成订单数量
     *
     * @return 已完成订单数量
     */
    public int getCompletedOrderCount() {
        return completedOrders.size();
    }

    /**
     * 手续费配置
     */
    public static class FeeConfig {
        private final BigDecimal makerFeeRate;
        private final BigDecimal takerFeeRate;

        public FeeConfig(BigDecimal makerFeeRate, BigDecimal takerFeeRate) {
            this.makerFeeRate = makerFeeRate;
            this.takerFeeRate = takerFeeRate;
        }

        public BigDecimal getMakerFeeRate() {
            return makerFeeRate;
        }

        public BigDecimal getTakerFeeRate() {
            return takerFeeRate;
        }
    }

    /**
     * 滑点配置
     */
    public static class SlippageConfig {
        private final BigDecimal marketOrderSlippage;
        private final BigDecimal limitOrderSlippage;

        public SlippageConfig(BigDecimal marketOrderSlippage, BigDecimal limitOrderSlippage) {
            this.marketOrderSlippage = marketOrderSlippage;
            this.limitOrderSlippage = limitOrderSlippage;
        }

        public BigDecimal getSlippageRate(Order.OrderType orderType) {
            return orderType == Order.OrderType.MARKET ? marketOrderSlippage : limitOrderSlippage;
        }
    }
}
