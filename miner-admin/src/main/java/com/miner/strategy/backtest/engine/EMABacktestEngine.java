package com.miner.strategy.backtest.engine;

import cn.hutool.core.date.DateUtil;
import com.miner.strategy.backtest.config.BacktestConfig;
import com.miner.strategy.backtest.model.BacktestPosition;
import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * EMA策略回测引擎
 * 用于执行EMA多头排列策略的回测
 */
@Slf4j
public class EMABacktestEngine extends BaseBacktestEngine {

    // 指标缓存
    private final Map<String, Map<String, Double>> indicatorCache = new ConcurrentHashMap<>();
    private static final int MIN_REQUIRED_BARS = 100;
    private static final int CACHE_SIZE = 1000;

    /**
     * 创建回测引擎
     *
     * @param config         回测配置
     * @param historicalData 历史数据
     */
    public EMABacktestEngine(BacktestConfig config, Map<String, List<KLineEntity>> historicalData) {
        super(config, historicalData);
        initializeIndicatorCache();
    }

    private void initializeIndicatorCache() {
        for (String symbol : separatedData.keySet()) {
            indicatorCache.put(symbol, new HashMap<>());
        }
    }

    @Override
    protected String getStrategyName() {
        return "EMA多头排列策略";
    }

    @Override
    protected void processTimestamp(long timestamp) {
        // 处理每个交易对
        for (String symbol : separatedData.keySet()) {
            try {
                Map<String, List<KLineEntity>> timeframeData = separatedData.get(symbol);

                // 获取该时间戳的K线数据
                KLineEntity currentKline = getCurrentKline(timeframeData, timestamp, config.getMainTimeframe());
                if (currentKline == null) {
                    continue;
                }

                // 更新指标缓存
                updateIndicatorCache(symbol, timestamp);

                // 1. 检查现有持仓是否触发止盈止损
                checkStopConditions(symbol, currentKline);

                // 2. 处理候选交易对
                if (candidates.containsKey(symbol)) {
                    checkCandidateEntry(symbol, timestamp);
                }

                // 3. 寻找新的候选交易对
                if (!activePositions.containsKey(symbol) && !candidates.containsKey(symbol)) {
                    findNewCandidate(symbol, timestamp);
                }
            } catch (Exception e) {
                log.error("处理交易对 {} 时发生错误: {}", symbol, e.getMessage());
            }
        }
    }

    private void updateIndicatorCache(String symbol, long timestamp) {
        Map<String, List<KLineEntity>> timeframeData = separatedData.get(symbol);
        List<KLineEntity> kline4h = getRecentKlines(timeframeData, timestamp, config.getMainTimeframe(), MIN_REQUIRED_BARS);

        if (kline4h.size() < MIN_REQUIRED_BARS) {
            return;
        }

        // 提取收盘价
        List<Double> closePrices = kline4h.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(java.util.stream.Collectors.toList());

        // 计算并缓存指标
        Map<String, Double> symbolCache = indicatorCache.get(symbol);
        symbolCache.put("emaFast", calculateEMA(closePrices, 0, config.getEmaFast()));
        symbolCache.put("emaMid", calculateEMA(closePrices, 0, config.getEmaMid()));
        symbolCache.put("emaSlow", calculateEMA(closePrices, 0, config.getEmaSlow()));
        symbolCache.put("atr", calculateATR(kline4h, 0, config.getAtrPeriod()));
        symbolCache.put("emaSlope", calculateEmaSlope(kline4h));

        // 清理过期缓存
        if (symbolCache.size() > CACHE_SIZE) {
            symbolCache.clear();
        }
    }

    /**
     * 检查持仓是否触发止盈止损
     */
    private void checkStopConditions(String symbol, KLineEntity currentKline) {
        BacktestPosition position = activePositions.get(symbol);
        if (position == null) {
            return;
        }

        BigDecimal currentPrice = currentKline.getClose();
        BigDecimal currentLow = currentKline.getLow();
        BigDecimal currentHigh = currentKline.getHigh();

        // 检查是否超过36根K线未全部止盈
        if (position.getEntryTime() != null) {
            LocalDateTime entryTime = position.getEntryTime();
            LocalDateTime currentTime = LocalDateTime.ofInstant(
                Instant.ofEpochSecond(currentKline.getTs()), ZoneId.systemDefault());

            // 计算K线数量（4小时K线）
            long hoursBetween = ChronoUnit.HOURS.between(entryTime, currentTime);
            long klineCount = hoursBetween / 4; // 4小时K线

            if (klineCount >= 36 && !position.isPartialClosed()) {
                log.info("交易对 {} 超过36根K线未全部止盈，执行平仓", symbol);
                closePosition(position, currentKline.getTs(), currentPrice, "timeout");
                return;
            }
        }

        // 计算未实现盈亏比例
        BigDecimal entryPrice = position.getEntryPrice();
        double unrealizedPnlRatio = currentPrice.subtract(entryPrice)
            .divide(entryPrice, 8, RoundingMode.HALF_UP)
            .doubleValue();

        // 添加跟踪止损逻辑
        if (position.isPartialClosed()) {
            // 止盈1已触发，激活跟踪止损
            // 一旦盈利回撤超过50%，立即平仓

            // 获取上一次高点价格
            // 检查是否需要更新最高价
            if (position.getHighestPrice() == null || currentHigh.compareTo(position.getHighestPrice()) > 0) {
                position.setHighestPrice(currentHigh);
            }

            // 计算回撤比例
            if (position.getHighestPrice() != null) {
                BigDecimal drawdown = position.getHighestPrice().subtract(currentPrice)
                    .divide(position.getHighestPrice().subtract(position.getEntryPrice()), 8, RoundingMode.HALF_UP);

                // 如果回撤超过50%，触发跟踪止损
                if (drawdown.doubleValue() > 0.5 && currentPrice.compareTo(position.getEntryPrice()) > 0) {
                    closePosition(position, currentKline.getTs(), currentPrice, "trailing_stop");
                    return;
                }
            }
        } else if (unrealizedPnlRatio > 0.05) {
            // 如果尚未部分平仓但已经有5%以上收益，设置保本止损
            BigDecimal breakEvenStop = position.getEntryPrice().multiply(new BigDecimal("1.002")); // 保本+0.2%

            // 只有当原止损价低于保本价时才更新
            if (breakEvenStop.compareTo(position.getStopLossPrice()) > 0) {
                position.setStopLossPrice(breakEvenStop);
                log.debug("交易对 {} 更新为保本止损，价格: {}", symbol, breakEvenStop);
            }
        }

        // 检查是否触发止损
        if (currentLow.compareTo(position.getStopLossPrice()) <= 0) {
            closePosition(position, currentKline.getTs(), position.getStopLossPrice(), "stop_loss");
            return;
        }

        // 如果还没有部分平仓，检查是否触发第一个止盈点
        if (!position.isPartialClosed() &&
            currentHigh.compareTo(position.getTakeProfit1Price()) >= 0) {
            // 计算平仓仓位大小（50%）
            BigDecimal closeSize = position.getPositionSize().multiply(
                new BigDecimal("0.5")).setScale(8, RoundingMode.DOWN);
            BigDecimal remainingSize = position.getPositionSize().subtract(closeSize);

            // 创建部分平仓记录
            BacktestPosition closedPosition = createPartialClosedPosition(
                position, currentKline.getTs(), position.getTakeProfit1Price(),
                closeSize, "take_profit_1");

            // 记录交易
            positions.add(closedPosition);

            // 更新当前持仓
            position.setPositionSize(remainingSize);
            position.setPartialClosed(true);
            position.setStatus(BacktestPosition.PositionStatus.PARTIAL_CLOSED);

            // 初始化最高价跟踪
            position.setHighestPrice(position.getTakeProfit1Price());

            // 更新资金
            currentCapital += closedPosition.getPnl().doubleValue();
            if (config.getTradeFeeRate() > 0) {
                BigDecimal closingFee = position.getTakeProfit1Price().multiply(closeSize)
                    .multiply(BigDecimal.valueOf(config.getTradeFeeRate()));
                currentCapital -= closingFee.doubleValue();
            }

            log.debug("交易对 {} 触发止盈1，部分平仓50%，价格: {}, 盈亏: {}",
                symbol, position.getTakeProfit1Price(), closedPosition.getPnl());
        }

        // 检查是否触发第二个止盈点
        if (position.isPartialClosed() &&
            currentHigh.compareTo(position.getTakeProfit2Price()) >= 0) {
            closePosition(position, currentKline.getTs(), position.getTakeProfit2Price(), "take_profit_2");
        }
    }

    /**
     * 检查候选交易对是否满足入场条件
     */
    private void checkCandidateEntry(String symbol, long timestamp) {
        Map<String, List<KLineEntity>> timeframeData = separatedData.get(symbol);

        // 获取4小时K线数据
        List<KLineEntity> kline4h = getRecentKlines(timeframeData, timestamp, config.getMainTimeframe(), 100);

        if (kline4h.size() < config.getEmaSlow() + 1) {
            return;
        }

        // 获取当前价格
        BigDecimal currentPrice = kline4h.get(0).getClose();

        // 提取收盘价
        List<Double> closePrices = kline4h.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(java.util.stream.Collectors.toList());

        // 计算EMA指标
        double emaFast = calculateEMA(closePrices, 0, config.getEmaFast());
        double emaMid = calculateEMA(closePrices, 0, config.getEmaMid());
        double emaSlow = calculateEMA(closePrices, 0, config.getEmaSlow());

        // 计算EMA斜率
        double emaSlowSlope = calculateEmaSlope(kline4h);

        // 检查EMA多头排列条件
        boolean isEmaAligned = emaFast > emaMid && emaMid > emaSlow;
        boolean isInRetraceZone = isPriceInRetraceZone(kline4h, kline4h.get(0).getLow());

        if (isEmaAligned && isInRetraceZone) {
            // 计算ATR用于设置止损和止盈
            double atr = calculateATR(kline4h, 0, config.getAtrPeriod());
            if (atr <= 0) {
                return;
            }

            // 计算ATR百分比
            double atrPercent = (atr / currentPrice.doubleValue()) * 100;

            // 设置止损价 (3倍ATR)
            BigDecimal stopLossPrice = new BigDecimal(currentPrice.doubleValue() - atr * config.getStopLossMultiplier())
                .setScale(currentPrice.scale(), RoundingMode.DOWN);

            // 计算止盈价格（基于ATR倍数）
            BigDecimal takeProfitPrice1 = currentPrice.add(new BigDecimal(atr * config.getTakeProfitMultiplier1()))
                .setScale(currentPrice.scale(), RoundingMode.DOWN);
            BigDecimal takeProfitPrice2 = currentPrice.add(new BigDecimal(atr * config.getTakeProfitMultiplier2()))
                .setScale(currentPrice.scale(), RoundingMode.DOWN);

            // 打印详细的买入信号日志
//            log.info("\n========== 买入信号触发 ==========\n" +
//                    "交易对: {}\n" +
//                    "时间: {}\n" +
//                    "当前价格: {}\n" +
//                    "指标信息:\n" +
//                    "  - EMA快线({}): {}\n" +
//                    "  - EMA中线({}): {}\n" +
//                    "  - EMA慢线({}): {}\n" +
//                    "  - EMA斜率: {}%\n" +
//                    "  - ATR: {} ({}%)\n" +
//                    "交易条件:\n" +
//                    "  - EMA多头排列: {}\n" +
//                    "  - 回调区域: {}\n" +
//                    "交易设置:\n" +
//                    "  - 止损价: {} (距离: {}%)\n" +
//                    "  - 止盈价1: {} (距离: {}%)\n" +
//                    "  - 止盈价2: {} (距离: {}%)\n" +
//                    "================================",
//                symbol,
//                DateUtil.formatDateTime(new Date(timestamp)),
//                currentPrice,
//                config.getEmaFast(), String.format("%.2f", emaFast),
//                config.getEmaMid(), String.format("%.2f", emaMid),
//                config.getEmaSlow(), String.format("%.2f", emaSlow),
//                String.format("%.2f", emaSlowSlope),
//                String.format("%.2f", atr), String.format("%.2f", atrPercent),
//                isEmaAligned,
//                isInRetraceZone,
//                stopLossPrice, String.format("%.2f", (currentPrice.doubleValue() - stopLossPrice.doubleValue()) / currentPrice.doubleValue() * 100),
//                takeProfitPrice1, String.format("%.2f", (takeProfitPrice1.doubleValue() - currentPrice.doubleValue()) / currentPrice.doubleValue() * 100),
//                takeProfitPrice2, String.format("%.2f", (takeProfitPrice2.doubleValue() - currentPrice.doubleValue()) / currentPrice.doubleValue() * 100));

            // 执行开仓
            openPosition(symbol, timestamp, currentPrice, stopLossPrice, takeProfitPrice1, takeProfitPrice2);

            // 移除候选状态
            candidates.remove(symbol);
        }
    }

    /**
     * 计算EMA指标
     */
    private double calculateEMA(List<Double> prices, int offset, int period) {
        try {
            if (prices.size() < offset + period) {
                return 0;
            }

            // 使用更稳定的EMA计算方法
            double alpha = 2.0 / (period + 1);
            double ema = prices.get(offset + period - 1);

            for (int i = offset + period - 2; i >= offset; i--) {
                ema = alpha * prices.get(i) + (1 - alpha) * ema;
            }

            // 验证计算结果
            if (Double.isNaN(ema) || Double.isInfinite(ema)) {
                log.error("EMA计算结果异常 - period: {}, offset: {}, result: {}", period, offset, ema);
                return 0;
            }

            return ema;
        } catch (Exception e) {
            log.error("计算EMA时发生错误: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 计算ATR指标
     */
    private double calculateATR(List<KLineEntity> klineList, int offset, int period) {
        try {
            if (klineList.size() < offset + period + 1) {
                return 0;
            }

            // 使用更稳定的ATR计算方法
            double[] trValues = new double[period];
            double sum = 0;

            for (int i = 0; i < period; i++) {
                int currIdx = offset + i;
                int prevIdx = offset + i + 1;

                if (currIdx >= klineList.size() || prevIdx >= klineList.size()) {
                    continue;
                }

                KLineEntity curr = klineList.get(currIdx);
                KLineEntity prev = klineList.get(prevIdx);

                double high = curr.getHigh().doubleValue();
                double low = curr.getLow().doubleValue();
                double prevClose = prev.getClose().doubleValue();

                double tr1 = high - low;
                double tr2 = Math.abs(high - prevClose);
                double tr3 = Math.abs(low - prevClose);

                trValues[i] = Math.max(Math.max(tr1, tr2), tr3);
                sum += trValues[i];
            }

            double atr = sum / period;

            // 验证计算结果
            if (Double.isNaN(atr) || Double.isInfinite(atr) || atr <= 0) {
                log.error("ATR计算结果异常 - period: {}, offset: {}, result: {}", period, offset, atr);
                return 0;
            }

            return atr;
        } catch (Exception e) {
            log.error("计算ATR时发生错误: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 计算EMA斜率（判断趋势强度）
     */
    private double calculateEmaSlope(List<KLineEntity> klineList) {
        try {
            if (klineList.size() < config.getEmaSlow() + 10) {
                return 0;
            }

            List<Double> closePrices = klineList.stream()
                .map(k -> k.getClose().doubleValue())
                .collect(java.util.stream.Collectors.toList());

            // 计算当前、5根K线前和10根K线前的EMA55
            double currentEma = calculateEMA(closePrices, 0, config.getEmaSlow());
            double previousEma5 = calculateEMA(closePrices, 5, config.getEmaSlow());
            double previousEma10 = calculateEMA(closePrices, 10, config.getEmaSlow());

            // 计算斜率
            double slope5 = (currentEma - previousEma5) / previousEma5 * 100;
            double slope10 = (currentEma - previousEma10) / previousEma10 * 100;

            // 验证计算结果
            if (Double.isNaN(slope5) || Double.isInfinite(slope5) ||
                Double.isNaN(slope10) || Double.isInfinite(slope10)) {
                log.error("EMA斜率计算结果异常 - slope5: {}, slope10: {}", slope5, slope10);
                return 0;
            }

            // 判断趋势是否加速
            boolean isAccelerating = slope5 > slope10 / 2;
            return isAccelerating ? slope5 * 1.2 : slope5;
        } catch (Exception e) {
            log.error("计算EMA斜率时发生错误: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 检查价格是否在回调区域
     */
    private boolean isPriceInRetraceZone(List<KLineEntity> klineList, BigDecimal low) {
        if (klineList.size() < config.getEmaSlow() + 1) {
            return false;
        }

        // 提取收盘价
        List<Double> closePrices = klineList.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(java.util.stream.Collectors.toList());

        // 计算EMA指标
        double emaMid = calculateEMA(closePrices, 0, config.getEmaMid());
        double emaSlow = calculateEMA(closePrices, 0, config.getEmaSlow());

        // 计算回调目标区域
        double distance = emaMid - emaSlow;
        double targetPrice = emaMid - distance * 0.8; // 80%回调深度

        boolean isInZone = low.doubleValue() <= targetPrice;

        if (isInZone) {
            log.debug("价格在回调区域 - 当前价格: {}, 目标价格: {}, 距离: {}, EMA中: {}, EMA慢: {}",
                low,
                String.format("%.2f", targetPrice),
                String.format("%.2f", distance),
                String.format("%.2f", emaMid),
                String.format("%.2f", emaSlow));
        }

        return isInZone;
    }

    /**
     * 寻找新的符合条件的候选交易对
     */
    private void findNewCandidate(String symbol, long timestamp) {
        Map<String, List<KLineEntity>> timeframeData = separatedData.get(symbol);

        // 获取4小时K线数据
        List<KLineEntity> kline4h = getRecentKlines(timeframeData, timestamp, config.getMainTimeframe(), 100);

        if (kline4h.isEmpty()) {
            return;
        }

        // 计算当前价格
        BigDecimal currentPrice = kline4h.get(0).getClose();
        if (currentPrice == null || currentPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        // 提取收盘价
        List<Double> closePrices = kline4h.stream()
            .map(k -> k.getClose().doubleValue())
            .collect(java.util.stream.Collectors.toList());

        // 计算EMA指标
        double emaFast = calculateEMA(closePrices, 0, config.getEmaFast());
        double emaMid = calculateEMA(closePrices, 0, config.getEmaMid());
        double emaSlow = calculateEMA(closePrices, 0, config.getEmaSlow());

        // 检查EMA55斜率
        double emaSlowSlope = calculateEmaSlope(kline4h);

        // 策略1: EMA多头排列回调策略
        if (emaFast > emaMid && emaMid > emaSlow && isPriceInRetraceZone(kline4h, kline4h.get(0).getLow())) {
            // 添加为候选交易对
            candidates.put(symbol, timestamp);
            log.debug("发现符合回调区间的交易对: {}, 当前价格: {}", symbol, currentPrice);
        }
        // 策略2: EMA55趋势向上，检查价格是否在EMA55下方的动态支撑位
        else if (emaSlowSlope > 0.1) {
            // 检查动态支撑位
            SupportZoneResult supportZone = checkDynamicEma55SupportZone(currentPrice.doubleValue(), emaSlow, kline4h);

            if (supportZone.isInSupportZone()) {
                // 添加为候选交易对
                candidates.put(symbol, timestamp);
                log.debug("发现EMA55上升趋势(斜率={}%)且价格在动态支撑位的交易对: {}, 当前价格: {}, EMA55: {}, 波动率: {}%, 支撑区间: {}%~{}%",
                    String.format("%.2f", emaSlowSlope),
                    symbol, currentPrice, String.format("%.4f", emaSlow),
                    String.format("%.2f", supportZone.getAtrPercent()),
                    String.format("%.2f", supportZone.getMinDistancePercent()),
                    String.format("%.2f", supportZone.getMaxDistancePercent()));
            }
        }
    }

    /**
     * 计算动态EMA55支撑区域
     */
    private SupportZoneResult checkDynamicEma55SupportZone(double price, double ema55, List<KLineEntity> klineList) {
        // 计算ATR作为波动率指标
        double atr = calculateATR(klineList, 0, config.getAtrPeriod());

        // 将ATR转换为价格的百分比表示
        double atrPercent = (atr / price) * 100;

        // 基于波动率动态调整支撑位区间的上下边界
        double minDistancePercent = 0.5 + (atrPercent * 0.5);
        double maxDistancePercent = Math.min(1.5 + (atrPercent * 0.8), 5.0);

        // 计算当前价格与EMA55的实际距离(百分比)
        double distancePercent = (ema55 - price) / ema55 * 100;

        // 判断价格是否在动态支撑区域内
        boolean inSupportZone = distancePercent > minDistancePercent && distancePercent < maxDistancePercent;

        return new SupportZoneResult(inSupportZone, atrPercent, minDistancePercent, maxDistancePercent, distancePercent);
    }

    /**
     * 动态支撑区域结果类
     */
    private static class SupportZoneResult {
        private final boolean inSupportZone;
        private final double atrPercent;
        private final double minDistancePercent;
        private final double maxDistancePercent;
        private final double actualDistancePercent;

        public SupportZoneResult(boolean inSupportZone, double atrPercent, double minDistancePercent,
                                 double maxDistancePercent, double actualDistancePercent) {
            this.inSupportZone = inSupportZone;
            this.atrPercent = atrPercent;
            this.minDistancePercent = minDistancePercent;
            this.maxDistancePercent = maxDistancePercent;
            this.actualDistancePercent = actualDistancePercent;
        }

        public boolean isInSupportZone() {
            return inSupportZone;
        }

        public double getAtrPercent() {
            return atrPercent;
        }

        public double getMinDistancePercent() {
            return minDistancePercent;
        }

        public double getMaxDistancePercent() {
            return maxDistancePercent;
        }

        public double getActualDistancePercent() {
            return actualDistancePercent;
        }
    }
}
