package com.miner.strategy.backtest.core;

import com.miner.strategy.backtest.config.BacktestConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * 策略工厂
 * 负责策略的创建和注册管理
 * 
 * <AUTHOR>
 */
@Slf4j
public class StrategyFactory {
    
    private static final StrategyFactory INSTANCE = new StrategyFactory();
    
    private final Map<String, StrategyDescriptor> strategyRegistry;
    
    private StrategyFactory() {
        this.strategyRegistry = new ConcurrentHashMap<>();
    }
    
    public static StrategyFactory getInstance() {
        return INSTANCE;
    }
    
    /**
     * 注册策略
     * 
     * @param name 策略名称
     * @param supplier 策略供应商
     * @param description 策略描述
     * @param version 策略版本
     */
    public void registerStrategy(String name, Supplier<TradingStrategy> supplier, 
                               String description, String version) {
        StrategyDescriptor descriptor = new StrategyDescriptor(name, supplier, description, version);
        strategyRegistry.put(name, descriptor);
        log.info("注册策略: {} (版本: {}) - {}", name, version, description);
    }
    
    /**
     * 注册策略（简化版本）
     * 
     * @param name 策略名称
     * @param supplier 策略供应商
     */
    public void registerStrategy(String name, Supplier<TradingStrategy> supplier) {
        registerStrategy(name, supplier, "无描述", "1.0.0");
    }
    
    /**
     * 创建策略实例
     * 
     * @param name 策略名称
     * @param config 回测配置
     * @return 策略实例
     */
    public TradingStrategy createStrategy(String name, BacktestConfig config) {
        StrategyDescriptor descriptor = strategyRegistry.get(name);
        if (descriptor == null) {
            throw new IllegalArgumentException("未找到策略: " + name);
        }
        
        try {
            TradingStrategy strategy = descriptor.getSupplier().get();
            strategy.initialize(config);
            log.debug("创建策略实例: {}", name);
            return strategy;
        } catch (Exception e) {
            log.error("创建策略实例失败: {}", name, e);
            throw new RuntimeException("创建策略实例失败: " + name, e);
        }
    }
    
    /**
     * 取消注册策略
     * 
     * @param name 策略名称
     */
    public void unregisterStrategy(String name) {
        StrategyDescriptor removed = strategyRegistry.remove(name);
        if (removed != null) {
            log.info("取消注册策略: {}", name);
        }
    }
    
    /**
     * 检查策略是否已注册
     * 
     * @param name 策略名称
     * @return 是否已注册
     */
    public boolean isStrategyRegistered(String name) {
        return strategyRegistry.containsKey(name);
    }
    
    /**
     * 获取所有已注册的策略名称
     * 
     * @return 策略名称集合
     */
    public java.util.Set<String> getRegisteredStrategyNames() {
        return strategyRegistry.keySet();
    }
    
    /**
     * 获取策略描述符
     * 
     * @param name 策略名称
     * @return 策略描述符
     */
    public StrategyDescriptor getStrategyDescriptor(String name) {
        return strategyRegistry.get(name);
    }
    
    /**
     * 获取已注册策略数量
     * 
     * @return 策略数量
     */
    public int getRegisteredStrategyCount() {
        return strategyRegistry.size();
    }
    
    /**
     * 清空所有注册的策略
     */
    public void clear() {
        strategyRegistry.clear();
        log.info("已清空所有注册的策略");
    }
    
    /**
     * 策略描述符
     */
    public static class StrategyDescriptor {
        private final String name;
        private final Supplier<TradingStrategy> supplier;
        private final String description;
        private final String version;
        
        public StrategyDescriptor(String name, Supplier<TradingStrategy> supplier, 
                                String description, String version) {
            this.name = name;
            this.supplier = supplier;
            this.description = description;
            this.version = version;
        }
        
        public String getName() {
            return name;
        }
        
        public Supplier<TradingStrategy> getSupplier() {
            return supplier;
        }
        
        public String getDescription() {
            return description;
        }
        
        public String getVersion() {
            return version;
        }
        
        @Override
        public String toString() {
            return String.format("StrategyDescriptor{name='%s', version='%s', description='%s'}", 
                               name, version, description);
        }
    }
}
