package com.miner.strategy.backtest.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 回测配置类
 * 用于设置回测参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BacktestConfig {
    // 回测开始日期
    private LocalDateTime startDate;

    // 回测结束日期
    private LocalDateTime endDate;

    // 回测交易对列表
    private List<String> symbols;

    // 初始资金量
    private double initialCapital;

    // 每次开仓占总资金比例
    private double positionSizeRatio;

    // K线周期: 4h, 1h, 30m, 15m, 5m, 1m等
    private String mainTimeframe;
    private String subTimeframe;

    // TASK-001 新增配置项 - 数据源配置
    private String dataSource; // database, csv, exchange
    private String csvFilePath; // CSV文件路径，支持多文件用逗号分隔

    // 策略参数
    private int emaFast;
    private int emaMid;
    private int emaSlow;

    private int rsiPeriod;
    private double rsiOversold;
    private double rsiRising;
    private double entryScoreThreshold;

    private int atrPeriod;
    private double stopLossMultiplier;
    private double takeProfitMultiplier1;
    private double takeProfitMultiplier2;

    // EMA多头排列策略相关参数
    private int requiredRisingKlines; // 需要连续上涨的K线数量
    private double volumeMultiplier;  // 成交量倍数要求
    private double volatilityMultiplier; // 波动率倍数要求

    // 手续费率
    @Builder.Default
    private double tradeFeeRate = 0.001;

    // 滑点设置(百分比)
    @Builder.Default
    private double slippageRatio = 0.001;

    private Double retracementRatio; // 回调比例

    /**
     * 创建默认的回测配置
     */
    public static BacktestConfig createDefault() {
        return BacktestConfig.builder()
            .initialCapital(10000.0)
            .positionSizeRatio(0.02) // 每次开仓占总资金的2%
            .mainTimeframe("4h")
            .subTimeframe("5m")
            .dataSource("database") // 默认使用数据库
            .emaFast(9)
            .emaMid(21)
            .emaSlow(55)
            .atrPeriod(14)
            .stopLossMultiplier(2.0)  // 缩小止损距离，从3.0倍降到2.0倍
            .takeProfitMultiplier1(10.0)
            .takeProfitMultiplier2(20.0)
            .tradeFeeRate(0.001) // 0.1%手续费
            .slippageRatio(0.001) // 0.1%滑点
            // EMA多头排列策略相关参数默认值
            .requiredRisingKlines(4)
            .volumeMultiplier(1.2)
            .volatilityMultiplier(2.5)
            .build();
    }
}
