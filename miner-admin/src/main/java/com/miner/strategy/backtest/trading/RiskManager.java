package com.miner.strategy.backtest.trading;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 风险控制管理器
 * 负责风险监控、限制和控制
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public class RiskManager {

    // 风险配置
    private final RiskConfig riskConfig;

    // 账户管理器
    private final Account account;

    // 持仓管理器
    private final PositionManager positionManager;

    // 风险统计
    private final Map<String, SymbolRiskStats> symbolRiskStats = new ConcurrentHashMap<>();

    // 全局风险统计
    private final GlobalRiskStats globalRiskStats = new GlobalRiskStats();

    // 风险事件记录
    private final Map<LocalDateTime, RiskEvent> riskEvents = new ConcurrentHashMap<>();

    public RiskManager(RiskConfig riskConfig, Account account, PositionManager positionManager) {
        this.riskConfig = riskConfig;
        this.account = account;
        this.positionManager = positionManager;
    }

    /**
     * 检查开仓风险
     *
     * @param symbol   交易对
     * @param side     持仓方向
     * @param quantity 数量
     * @param price    价格
     * @param leverage 杠杆倍数
     * @return 风险检查结果
     */
    public RiskCheckResult checkOpenPositionRisk(String symbol, Position.PositionSide side,
                                                 BigDecimal quantity, BigDecimal price, int leverage) {

        // 1. 检查杠杆限制
        if (leverage > riskConfig.getMaxLeverage()) {
            return RiskCheckResult.reject("杠杆倍数超过限制: " + leverage + " > " + riskConfig.getMaxLeverage());
        }

        // 2. 检查单笔交易金额限制
        BigDecimal orderValue = quantity.multiply(price);
        BigDecimal maxOrderValue = account.getTotalEquity().multiply(riskConfig.getMaxOrderSizeRatio());
        if (orderValue.compareTo(maxOrderValue) > 0) {
            return RiskCheckResult.reject("单笔交易金额超过限制: " + orderValue + " > " + maxOrderValue);
        }

        // 3. 检查单个交易对持仓限制
        Position existingPosition = positionManager.getPosition(symbol);
        if (existingPosition != null) {
            BigDecimal totalValue = existingPosition.getPositionValue().add(orderValue);
            BigDecimal maxSymbolValue = account.getTotalEquity().multiply(riskConfig.getMaxSymbolExposureRatio());
            if (totalValue.compareTo(maxSymbolValue) > 0) {
                return RiskCheckResult.reject("单个交易对持仓超过限制: " + totalValue + " > " + maxSymbolValue);
            }
        }

        // 4. 检查总持仓限制
        BigDecimal totalExposure = calculateTotalExposure().add(orderValue);
        BigDecimal maxTotalExposure = account.getTotalEquity().multiply(riskConfig.getMaxTotalExposureRatio());
        if (totalExposure.compareTo(maxTotalExposure) > 0) {
            return RiskCheckResult.reject("总持仓超过限制: " + totalExposure + " > " + maxTotalExposure);
        }

        // 5. 检查账户回撤限制
        if (account.isRiskControlTriggered()) {
            return RiskCheckResult.reject("账户回撤超过限制，禁止开仓");
        }

        // 6. 检查交易对风险统计
        SymbolRiskStats stats = symbolRiskStats.get(symbol);
        if (stats != null && stats.isRiskLimitReached()) {
            return RiskCheckResult.reject("交易对风险限制已达到: " + symbol);
        }

        return RiskCheckResult.approve();
    }

    /**
     * 检查平仓风险
     *
     * @param positionId    持仓ID
     * @param closeQuantity 平仓数量
     * @return 风险检查结果
     */
    public RiskCheckResult checkClosePositionRisk(String positionId, BigDecimal closeQuantity) {
        Position position = positionManager.findPositionById(positionId);
        if (position == null) {
            return RiskCheckResult.reject("持仓不存在: " + positionId);
        }

        if (closeQuantity.compareTo(position.getQuantity()) > 0) {
            return RiskCheckResult.reject("平仓数量超过持仓数量");
        }

        return RiskCheckResult.approve();
    }

    /**
     * 更新风险统计
     *
     * @param symbol       交易对
     * @param currentPrice 当前价格
     */
    public void updateRiskStats(String symbol, BigDecimal currentPrice) {
        // 更新交易对风险统计
        SymbolRiskStats stats = symbolRiskStats.computeIfAbsent(symbol, k -> new SymbolRiskStats(symbol));
        stats.updatePrice(currentPrice);

        Position position = positionManager.getPosition(symbol);
        if (position != null) {
            stats.updatePosition(position);
        }

        // 更新全局风险统计
        updateGlobalRiskStats();

        // 检查风险事件
        checkRiskEvents();
    }

    /**
     * 更新全局风险统计
     */
    private void updateGlobalRiskStats() {
        globalRiskStats.totalEquity = account.getTotalEquity();
        globalRiskStats.availableBalance = account.getAvailableBalance();
        globalRiskStats.usedMargin = account.getUsedMargin();
        globalRiskStats.unrealizedPnl = account.getUnrealizedPnl();
        globalRiskStats.currentLeverage = account.getCurrentLeverage();
        globalRiskStats.currentDrawdown = account.getCurrentDrawdownRatio();
        globalRiskStats.totalExposure = calculateTotalExposure();
        globalRiskStats.activePositionCount = positionManager.getActivePositionCount();
        globalRiskStats.updateTime = LocalDateTime.now();
    }

    /**
     * 检查风险事件
     */
    private void checkRiskEvents() {
        LocalDateTime now = LocalDateTime.now();

        // 检查账户回撤风险
        if (account.getCurrentDrawdownRatio().compareTo(riskConfig.getMaxDrawdownRatio()) > 0) {
            recordRiskEvent(now, RiskEventType.ACCOUNT_DRAWDOWN_EXCEEDED,
                "账户回撤超过限制: " + account.getCurrentDrawdownRatio());
        }

        // 检查杠杆风险
        if (account.getCurrentLeverage().compareTo(BigDecimal.valueOf(riskConfig.getMaxLeverage())) > 0) {
            recordRiskEvent(now, RiskEventType.LEVERAGE_EXCEEDED,
                "杠杆倍数超过限制: " + account.getCurrentLeverage());
        }

        // 检查总持仓风险
        BigDecimal exposureRatio = globalRiskStats.totalExposure.divide(account.getTotalEquity(), 4, RoundingMode.HALF_UP);
        if (exposureRatio.compareTo(riskConfig.getMaxTotalExposureRatio()) > 0) {
            recordRiskEvent(now, RiskEventType.TOTAL_EXPOSURE_EXCEEDED,
                "总持仓比例超过限制: " + exposureRatio);
        }
    }

    /**
     * 记录风险事件
     *
     * @param time    时间
     * @param type    事件类型
     * @param message 消息
     */
    private void recordRiskEvent(LocalDateTime time, RiskEventType type, String message) {
        RiskEvent event = new RiskEvent(type, message, time);
        riskEvents.put(time, event);

        log.warn("风险事件: {} - {}", type, message);

        // 根据风险事件类型执行相应的风控措施
        handleRiskEvent(event);
    }

    /**
     * 处理风险事件
     *
     * @param event 风险事件
     */
    private void handleRiskEvent(RiskEvent event) {
        switch (event.getType()) {
            case ACCOUNT_DRAWDOWN_EXCEEDED:
                // 强制平仓所有持仓
                forceCloseAllPositions("账户回撤超限强制平仓");
                break;
            case LEVERAGE_EXCEEDED:
                // 部分平仓降低杠杆
                reducePositionSize("杠杆超限部分平仓");
                break;
            case TOTAL_EXPOSURE_EXCEEDED:
                // 禁止新开仓
                log.warn("总持仓超限，禁止新开仓");
                break;
        }
    }

    /**
     * 强制平仓所有持仓
     *
     * @param reason 平仓原因
     */
    private void forceCloseAllPositions(String reason) {
        for (Position position : positionManager.getActivePositions()) {
            positionManager.closePosition(position.getPositionId(), position.getCurrentPrice(), BigDecimal.ZERO);
            log.warn("强制平仓: {} - {}", position.getPositionId(), reason);
        }
    }

    /**
     * 减少持仓规模
     *
     * @param reason 减仓原因
     */
    private void reducePositionSize(String reason) {
        for (Position position : positionManager.getActivePositions()) {
            BigDecimal reduceQuantity = position.getQuantity().multiply(BigDecimal.valueOf(0.5)); // 减仓50%
            positionManager.partialClosePosition(position.getPositionId(), reduceQuantity,
                position.getCurrentPrice(), BigDecimal.ZERO);
            log.warn("部分平仓: {} 减仓 {} - {}", position.getPositionId(), reduceQuantity, reason);
        }
    }

    /**
     * 计算总持仓价值
     *
     * @return 总持仓价值
     */
    private BigDecimal calculateTotalExposure() {
        return positionManager.getActivePositions().stream()
            .map(Position::getPositionValue)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取风险摘要
     *
     * @return 风险摘要
     */
    public String getRiskSummary() {
        return String.format(
            "风险摘要: 总权益=%.2f, 杠杆=%.2fx, 回撤=%.2f%%, 总持仓=%.2f, 活跃持仓=%d, 风险事件=%d",
            globalRiskStats.totalEquity, globalRiskStats.currentLeverage,
            globalRiskStats.currentDrawdown.multiply(BigDecimal.valueOf(100)),
            globalRiskStats.totalExposure, globalRiskStats.activePositionCount, riskEvents.size()
        );
    }

    /**
     * 风险配置
     */
    @Data
    public static class RiskConfig {
        private int maxLeverage = 20;                                    // 最大杠杆倍数
        private BigDecimal maxDrawdownRatio = BigDecimal.valueOf(0.25);  // 最大回撤比例 25%
        private BigDecimal maxOrderSizeRatio = BigDecimal.valueOf(0.1);  // 单笔订单最大比例 10%
        private BigDecimal maxSymbolExposureRatio = BigDecimal.valueOf(0.3); // 单个交易对最大持仓比例 30%
        private BigDecimal maxTotalExposureRatio = BigDecimal.valueOf(0.8);  // 总持仓最大比例 80%
    }

    /**
     * 风险检查结果
     */
    @Data
    public static class RiskCheckResult {
        private final boolean approved;
        private final String message;

        public static RiskCheckResult approve() {
            return new RiskCheckResult(true, "风险检查通过");
        }

        public static RiskCheckResult reject(String reason) {
            return new RiskCheckResult(false, reason);
        }
    }

    /**
     * 交易对风险统计
     */
    @Data
    private static class SymbolRiskStats {
        private final String symbol;
        private BigDecimal currentPrice;
        private BigDecimal maxPrice;
        private BigDecimal minPrice;
        private BigDecimal volatility;
        private int consecutiveLosses;
        private LocalDateTime updateTime;

        public SymbolRiskStats(String symbol) {
            this.symbol = symbol;
            this.consecutiveLosses = 0;
        }

        public void updatePrice(BigDecimal price) {
            this.currentPrice = price;
            if (maxPrice == null || price.compareTo(maxPrice) > 0) {
                maxPrice = price;
            }
            if (minPrice == null || price.compareTo(minPrice) < 0) {
                minPrice = price;
            }
            updateTime = LocalDateTime.now();
        }

        public void updatePosition(Position position) {
            // 更新连续亏损次数等统计
        }

        public boolean isRiskLimitReached() {
            return consecutiveLosses >= 5; // 连续亏损5次
        }
    }

    /**
     * 全局风险统计
     */
    @Data
    private static class GlobalRiskStats {
        private BigDecimal totalEquity;
        private BigDecimal availableBalance;
        private BigDecimal usedMargin;
        private BigDecimal unrealizedPnl;
        private BigDecimal currentLeverage;
        private BigDecimal currentDrawdown;
        private BigDecimal totalExposure;
        private int activePositionCount;
        private LocalDateTime updateTime;
    }

    /**
     * 风险事件
     */
    @Data
    private static class RiskEvent {
        private final RiskEventType type;
        private final String message;
        private final LocalDateTime time;
    }

    /**
     * 风险事件类型
     */
    private enum RiskEventType {
        ACCOUNT_DRAWDOWN_EXCEEDED,  // 账户回撤超限
        LEVERAGE_EXCEEDED,          // 杠杆超限
        TOTAL_EXPOSURE_EXCEEDED,    // 总持仓超限
        SYMBOL_RISK_EXCEEDED        // 交易对风险超限
    }
}
