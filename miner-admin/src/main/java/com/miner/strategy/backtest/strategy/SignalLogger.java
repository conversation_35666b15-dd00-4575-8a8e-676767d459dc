package com.miner.strategy.backtest.strategy;

import com.miner.strategy.backtest.core.event.SignalEvent;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 信号生成日志记录器
 * 记录策略生成的所有交易信号，用于分析和调试
 * 
 * <AUTHOR>
 */
@Slf4j
public class SignalLogger {
    
    private static final SignalLogger INSTANCE = new SignalLogger();
    
    // 信号记录存储
    private final List<SignalRecord> signalRecords = new CopyOnWriteArrayList<>();
    
    // 按策略分组的信号统计
    private final Map<String, SignalStatistics> strategyStatistics = new ConcurrentHashMap<>();
    
    // 日志配置
    private boolean loggingEnabled = true;
    private boolean fileLoggingEnabled = true;
    private String logFilePath = "logs/signals/";
    private int maxRecordsInMemory = 10000;
    
    // 时间格式化器
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    
    private SignalLogger() {
        // 初始化日志目录
        createLogDirectory();
    }
    
    public static SignalLogger getInstance() {
        return INSTANCE;
    }
    
    /**
     * 记录信号事件
     * 
     * @param signal 信号事件
     */
    public void logSignal(SignalEvent signal) {
        if (!loggingEnabled) {
            return;
        }
        
        try {
            // 创建信号记录
            SignalRecord record = createSignalRecord(signal);
            
            // 添加到内存记录
            addToMemoryRecords(record);
            
            // 更新统计信息
            updateStatistics(record);
            
            // 写入文件日志
            if (fileLoggingEnabled) {
                writeToFile(record);
            }
            
            // 控制台日志
            log.info("信号记录: {} {} {} @ {} 置信度: {:.2f} 评分: {}", 
                    record.getSymbol(), record.getSignalType(), record.getQuantity(), 
                    record.getPrice(), record.getConfidence(), record.getEntryScore());
            
        } catch (Exception e) {
            log.error("记录信号失败: {}", signal, e);
        }
    }
    
    /**
     * 创建信号记录
     * 
     * @param signal 信号事件
     * @return 信号记录
     */
    private SignalRecord createSignalRecord(SignalEvent signal) {
        SignalRecord record = new SignalRecord();
        record.setTimestamp(LocalDateTime.now());
        record.setSignalId(signal.getEventId());
        record.setStrategyName(signal.getStrategyName());
        record.setSymbol(signal.getSymbol());
        record.setSignalType(signal.getSignalType().toString());
        record.setPrice(signal.getPrice());
        record.setQuantity(signal.getQuantity());
        record.setConfidence(signal.getConfidence());
        
        // 提取信号数据
        if (signal.getSignalData() != null) {
            Map<String, Object> signalData = signal.getSignalData();
            record.setEntryScore(getDoubleValue(signalData, "entryScore", 0.0));
            record.setEmaFast(getDoubleValue(signalData, "emaFast", 0.0));
            record.setEmaMid(getDoubleValue(signalData, "emaMid", 0.0));
            record.setEmaSlow(getDoubleValue(signalData, "emaSlow", 0.0));
            record.setRsi(getDoubleValue(signalData, "rsi", 50.0));
            record.setAtr(getDoubleValue(signalData, "atr", 0.0));
            record.setRetraceRatio(getDoubleValue(signalData, "retraceRatio", 0.0));
            record.setStopLoss(getBigDecimalValue(signalData, "stopLoss"));
            record.setTakeProfit1(getBigDecimalValue(signalData, "takeProfit1"));
            record.setTakeProfit2(getBigDecimalValue(signalData, "takeProfit2"));
        }
        
        return record;
    }
    
    /**
     * 添加到内存记录
     * 
     * @param record 信号记录
     */
    private void addToMemoryRecords(SignalRecord record) {
        signalRecords.add(record);
        
        // 控制内存使用，移除最老的记录
        while (signalRecords.size() > maxRecordsInMemory) {
            signalRecords.remove(0);
        }
    }
    
    /**
     * 更新统计信息
     * 
     * @param record 信号记录
     */
    private void updateStatistics(SignalRecord record) {
        String strategyName = record.getStrategyName();
        SignalStatistics stats = strategyStatistics.computeIfAbsent(strategyName, k -> new SignalStatistics(k));
        
        stats.incrementTotalSignals();
        stats.addConfidenceScore(record.getConfidence());
        stats.addEntryScore(record.getEntryScore());
        stats.setLastSignalTime(record.getTimestamp());
        
        // 按信号类型统计
        switch (record.getSignalType()) {
            case "BUY":
                stats.incrementBuySignals();
                break;
            case "SELL":
                stats.incrementSellSignals();
                break;
            case "CLOSE_LONG":
            case "CLOSE_SHORT":
                stats.incrementCloseSignals();
                break;
        }
        
        // 按交易对统计
        stats.incrementSymbolCount(record.getSymbol());
    }
    
    /**
     * 写入文件日志
     * 
     * @param record 信号记录
     */
    private void writeToFile(SignalRecord record) {
        String fileName = logFilePath + "signals_" + 
                         LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ".csv";
        
        try (PrintWriter writer = new PrintWriter(new FileWriter(fileName, true))) {
            // 写入CSV格式
            writer.println(formatRecordAsCsv(record));
        } catch (IOException e) {
            log.error("写入信号日志文件失败: {}", fileName, e);
        }
    }
    
    /**
     * 格式化记录为CSV
     * 
     * @param record 信号记录
     * @return CSV字符串
     */
    private String formatRecordAsCsv(SignalRecord record) {
        return String.format("%s,%s,%s,%s,%s,%s,%s,%.4f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.4f,%s,%s,%s",
                record.getTimestamp().format(TIMESTAMP_FORMAT),
                record.getSignalId(),
                record.getStrategyName(),
                record.getSymbol(),
                record.getSignalType(),
                record.getPrice(),
                record.getQuantity(),
                record.getConfidence(),
                record.getEntryScore(),
                record.getEmaFast(),
                record.getEmaMid(),
                record.getEmaSlow(),
                record.getRsi(),
                record.getAtr(),
                record.getRetraceRatio(),
                record.getStopLoss(),
                record.getTakeProfit1(),
                record.getTakeProfit2()
        );
    }
    
    /**
     * 获取策略信号统计
     * 
     * @param strategyName 策略名称
     * @return 信号统计
     */
    public SignalStatistics getStrategyStatistics(String strategyName) {
        return strategyStatistics.get(strategyName);
    }
    
    /**
     * 获取所有策略统计
     * 
     * @return 所有策略统计
     */
    public Map<String, SignalStatistics> getAllStatistics() {
        return new ConcurrentHashMap<>(strategyStatistics);
    }
    
    /**
     * 获取最近的信号记录
     * 
     * @param count 数量
     * @return 信号记录列表
     */
    public List<SignalRecord> getRecentSignals(int count) {
        int size = signalRecords.size();
        int fromIndex = Math.max(0, size - count);
        return new ArrayList<>(signalRecords.subList(fromIndex, size));
    }
    
    /**
     * 生成信号统计报告
     * 
     * @return 统计报告
     */
    public String generateStatisticsReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 信号生成统计报告 ===\n");
        report.append("生成时间: ").append(LocalDateTime.now().format(TIMESTAMP_FORMAT)).append("\n");
        report.append("总记录数: ").append(signalRecords.size()).append("\n\n");
        
        for (SignalStatistics stats : strategyStatistics.values()) {
            report.append("策略: ").append(stats.getStrategyName()).append("\n");
            report.append("  总信号数: ").append(stats.getTotalSignals()).append("\n");
            report.append("  买入信号: ").append(stats.getBuySignals()).append("\n");
            report.append("  卖出信号: ").append(stats.getSellSignals()).append("\n");
            report.append("  平仓信号: ").append(stats.getCloseSignals()).append("\n");
            report.append("  平均置信度: ").append(String.format("%.4f", stats.getAverageConfidence())).append("\n");
            report.append("  平均评分: ").append(String.format("%.2f", stats.getAverageEntryScore())).append("\n");
            report.append("  交易对数量: ").append(stats.getSymbolCounts().size()).append("\n");
            report.append("  最后信号时间: ").append(stats.getLastSignalTime()).append("\n\n");
        }
        
        return report.toString();
    }
    
    /**
     * 清理历史记录
     * 
     * @param keepDays 保留天数
     */
    public void cleanupOldRecords(int keepDays) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(keepDays);
        
        signalRecords.removeIf(record -> record.getTimestamp().isBefore(cutoffTime));
        
        log.info("清理 {} 天前的信号记录，当前记录数: {}", keepDays, signalRecords.size());
    }
    
    /**
     * 创建日志目录
     */
    private void createLogDirectory() {
        try {
            java.nio.file.Files.createDirectories(java.nio.file.Paths.get(logFilePath));
        } catch (IOException e) {
            log.error("创建日志目录失败: {}", logFilePath, e);
        }
    }
    
    /**
     * 辅助方法：获取Double值
     */
    private double getDoubleValue(Map<String, Object> map, String key, double defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return defaultValue;
    }
    
    /**
     * 辅助方法：获取BigDecimal值
     */
    private java.math.BigDecimal getBigDecimalValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof java.math.BigDecimal) {
            return (java.math.BigDecimal) value;
        }
        return null;
    }
    
    // Getter和Setter方法
    public void setLoggingEnabled(boolean loggingEnabled) {
        this.loggingEnabled = loggingEnabled;
    }
    
    public void setFileLoggingEnabled(boolean fileLoggingEnabled) {
        this.fileLoggingEnabled = fileLoggingEnabled;
    }
    
    public void setLogFilePath(String logFilePath) {
        this.logFilePath = logFilePath;
        createLogDirectory();
    }
    
    /**
     * 信号记录数据类
     */
    @Data
    public static class SignalRecord {
        private LocalDateTime timestamp;
        private String signalId;
        private String strategyName;
        private String symbol;
        private String signalType;
        private java.math.BigDecimal price;
        private java.math.BigDecimal quantity;
        private double confidence;
        private double entryScore;
        private double emaFast;
        private double emaMid;
        private double emaSlow;
        private double rsi;
        private double atr;
        private double retraceRatio;
        private java.math.BigDecimal stopLoss;
        private java.math.BigDecimal takeProfit1;
        private java.math.BigDecimal takeProfit2;
    }
    
    /**
     * 信号统计数据类
     */
    @Data
    public static class SignalStatistics {
        private final String strategyName;
        private long totalSignals = 0;
        private long buySignals = 0;
        private long sellSignals = 0;
        private long closeSignals = 0;
        private double totalConfidence = 0.0;
        private double totalEntryScore = 0.0;
        private LocalDateTime lastSignalTime;
        private final Map<String, Long> symbolCounts = new ConcurrentHashMap<>();
        
        public SignalStatistics(String strategyName) {
            this.strategyName = strategyName;
        }
        
        public void incrementTotalSignals() {
            totalSignals++;
        }
        
        public void incrementBuySignals() {
            buySignals++;
        }
        
        public void incrementSellSignals() {
            sellSignals++;
        }
        
        public void incrementCloseSignals() {
            closeSignals++;
        }
        
        public void addConfidenceScore(double confidence) {
            totalConfidence += confidence;
        }
        
        public void addEntryScore(double entryScore) {
            totalEntryScore += entryScore;
        }
        
        public void incrementSymbolCount(String symbol) {
            symbolCounts.merge(symbol, 1L, Long::sum);
        }
        
        public double getAverageConfidence() {
            return totalSignals > 0 ? totalConfidence / totalSignals : 0.0;
        }
        
        public double getAverageEntryScore() {
            return totalSignals > 0 ? totalEntryScore / totalSignals : 0.0;
        }
    }
}
