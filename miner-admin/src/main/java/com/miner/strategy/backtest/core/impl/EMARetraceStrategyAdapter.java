package com.miner.strategy.backtest.core.impl;

import com.miner.strategy.backtest.config.BacktestConfig;
import com.miner.strategy.backtest.core.TradingStrategy;
import com.miner.strategy.backtest.core.event.MarketDataEvent;
import com.miner.strategy.backtest.core.event.SignalEvent;
import com.miner.strategy.backtest.indicators.TechnicalIndicators;
import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * EMA回调策略适配器
 * 将现有的EMA策略完整适配到新的策略框架中
 * 实现完整的EMA多头排列回调策略逻辑
 *
 * <AUTHOR>
 */
@Slf4j
public class EMARetraceStrategyAdapter implements TradingStrategy {

    private BacktestConfig config;
    private StrategyState state = StrategyState.INITIALIZED;
    private Map<String, Object> parameters = new HashMap<>();

    // 策略状态
    private final Map<String, StrategyContext> symbolContexts = new HashMap<>();

    // 策略参数（从原EMA策略移植）
    private int emaFast = 9;
    private int emaMid = 21;
    private int emaSlow = 55;
    private int rsiPeriod = 14;
    private double rsiOversold = 30.0;
    private double rsiRising = 50.0;
    private double entryScoreThreshold = 7.0;
    private int atrPeriod = 14;
    private double stopLossMultiplier = 2.0;
    private double takeProfitMultiplier1 = 10.0;
    private double takeProfitMultiplier2 = 20.0;
    private double retraceDepth = 0.8;

    // 信号生成统计
    private int totalSignals = 0;
    private int bullishSignals = 0;
    private int bearishSignals = 0;
    private LocalDateTime lastSignalTime;

    @Override
    public void initialize(BacktestConfig config) {
        this.config = config;

        // 设置策略参数
        this.emaFast = config.getEmaFast();
        this.emaMid = config.getEmaMid();
        this.emaSlow = config.getEmaSlow();
        this.rsiPeriod = config.getRsiPeriod();
        this.rsiOversold = config.getRsiOversold();
        this.rsiRising = config.getRsiRising();
        this.entryScoreThreshold = config.getEntryScoreThreshold();
        this.atrPeriod = config.getAtrPeriod();
        this.stopLossMultiplier = config.getStopLossMultiplier();
        this.takeProfitMultiplier1 = config.getTakeProfitMultiplier1();
        this.takeProfitMultiplier2 = config.getTakeProfitMultiplier2();

        // 更新参数映射
        updateParametersMap();

        // 为每个交易对初始化上下文
        for (String symbol : config.getSymbols()) {
            symbolContexts.put(symbol, new StrategyContext(symbol));
        }

        state = StrategyState.ACTIVE;
        log.info("EMA回调策略适配器初始化完成，交易对数量: {}", config.getSymbols().size());
    }

    @Override
    public List<SignalEvent> onMarketData(MarketDataEvent event) {
        if (state != StrategyState.ACTIVE) {
            return Collections.emptyList();
        }

        String symbol = event.getSymbol();
        StrategyContext context = symbolContexts.get(symbol);

        if (context == null) {
            return Collections.emptyList();
        }

        List<SignalEvent> signals = new ArrayList<>();

        try {
            // 更新上下文数据
            context.updateMarketData(event);

            // 检查是否有足够的数据进行分析
            if (!context.hasEnoughData()) {
                return signals;
            }

            // 执行策略逻辑
            SignalEvent signal = executeStrategyLogic(context, event);
            if (signal != null) {
                signals.add(signal);
            }

        } catch (Exception e) {
            log.error("处理交易对 {} 的市场数据时发生异常: {}", symbol, e.getMessage(), e);
        }

        return signals;
    }

    @Override
    public String getStrategyName() {
        return "EMARetraceStrategy";
    }

    @Override
    public String getVersion() {
        return "2.1.0";
    }

    @Override
    public String getDescription() {
        return "EMA回调策略 - 基于EMA多头排列的回调入场策略，集成完整的技术指标分析和信号评分系统";
    }

    @Override
    public Map<String, Object> getParameters() {
        return new HashMap<>(parameters);
    }

    @Override
    public void setParameters(Map<String, Object> parameters) {
        // 更新策略参数
        if (parameters.containsKey("emaFast")) {
            this.emaFast = (Integer) parameters.get("emaFast");
        }
        if (parameters.containsKey("emaMid")) {
            this.emaMid = (Integer) parameters.get("emaMid");
        }
        if (parameters.containsKey("emaSlow")) {
            this.emaSlow = (Integer) parameters.get("emaSlow");
        }
        if (parameters.containsKey("rsiPeriod")) {
            this.rsiPeriod = (Integer) parameters.get("rsiPeriod");
        }
        if (parameters.containsKey("rsiOversold")) {
            this.rsiOversold = (Double) parameters.get("rsiOversold");
        }
        if (parameters.containsKey("entryScoreThreshold")) {
            this.entryScoreThreshold = (Double) parameters.get("entryScoreThreshold");
        }

        // 更新参数映射
        updateParametersMap();

        log.info("更新策略参数: {}", parameters);
    }

    @Override
    public ParameterValidationResult validateParameters(Map<String, Object> parameters) {
        List<String> errors = new ArrayList<>();

        // 验证EMA参数
        Integer emaFast = (Integer) parameters.get("emaFast");
        Integer emaMid = (Integer) parameters.get("emaMid");
        Integer emaSlow = (Integer) parameters.get("emaSlow");

        if (emaFast == null || emaFast <= 0) {
            errors.add("emaFast必须大于0");
        }
        if (emaMid == null || emaMid <= 0) {
            errors.add("emaMid必须大于0");
        }
        if (emaSlow == null || emaSlow <= 0) {
            errors.add("emaSlow必须大于0");
        }

        if (emaFast != null && emaMid != null && emaSlow != null) {
            if (emaFast >= emaMid || emaMid >= emaSlow) {
                errors.add("EMA参数必须满足: emaFast < emaMid < emaSlow");
            }
        }

        // 验证RSI参数
        Integer rsiPeriod = (Integer) parameters.get("rsiPeriod");
        if (rsiPeriod == null || rsiPeriod <= 0) {
            errors.add("rsiPeriod必须大于0");
        }

        Double rsiOversold = (Double) parameters.get("rsiOversold");
        if (rsiOversold == null || rsiOversold < 0 || rsiOversold > 100) {
            errors.add("rsiOversold必须在0-100之间");
        }

        boolean valid = errors.isEmpty();
        String message = valid ? "参数验证通过" : "参数验证失败";

        return new ParameterValidationResult(valid, message, errors);
    }

    @Override
    public void reset() {
        symbolContexts.clear();
        if (config != null) {
            for (String symbol : config.getSymbols()) {
                symbolContexts.put(symbol, new StrategyContext(symbol));
            }
        }
        state = StrategyState.INITIALIZED;
        log.info("策略状态已重置");
    }

    @Override
    public StrategyState getState() {
        return state;
    }

    /**
     * 执行策略逻辑
     */
    private SignalEvent executeStrategyLogic(StrategyContext context, MarketDataEvent event) {
        try {
            // 1. 检查数据充足性
            if (!context.hasEnoughData()) {
                return null;
            }

            // 2. 计算技术指标
            IndicatorValues indicators = context.calculateIndicators();

            // 3. 检查EMA多头排列
            if (!indicators.isEMABullishAlignment()) {
                return null;
            }

            // 4. 计算入场评分
            double entryScore = calculateEntryScore(indicators, event);

            // 5. 检查评分是否达到阈值
            if (entryScore < entryScoreThreshold) {
                log.debug("入场评分不足: {} < {}, 交易对: {}", entryScore, entryScoreThreshold, event.getSymbol());
                return null;
            }

            // 6. 生成交易信号
            SignalEvent signal = generateTradingSignal(event, indicators, entryScore);

            if (signal != null) {
                totalSignals++;
                if (signal.getSignalType() == SignalEvent.SignalType.BUY) {
                    bullishSignals++;
                } else {
                    bearishSignals++;
                }
                lastSignalTime = LocalDateTime.now();

                log.info("生成交易信号: {} {} @ {}, 评分: {:.2f}, 置信度: {:.2f}",
                    signal.getSymbol(), signal.getSignalType(), signal.getPrice(),
                    entryScore, signal.getConfidence());
            }

            return signal;

        } catch (Exception e) {
            log.error("策略逻辑执行异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 计算入场评分
     */
    private double calculateEntryScore(IndicatorValues indicators, MarketDataEvent event) {
        double score = 0.0;

        // 1. EMA多头排列评分 (3分)
        if (indicators.isEMABullishAlignment()) {
            score += 3.0;

            // EMA间距评分 (额外1分)
            double emaSpread = (indicators.emaFast - indicators.emaSlow) / indicators.emaSlow;
            if (emaSpread > 0.02) { // 2%以上间距
                score += 1.0;
            }
        }

        // 2. RSI回调评分 (2分)
        if (indicators.rsi < rsiOversold) {
            score += 2.0;
        } else if (indicators.rsi < rsiRising) {
            score += 1.0; // 部分回调
        }

        // 3. 价格回调评分 (2分)
        double retraceRatio = calculateRetraceRatio(indicators);
        if (retraceRatio >= retraceDepth) {
            score += 2.0;
        } else if (retraceRatio >= retraceDepth * 0.5) {
            score += 1.0;
        }

        // 4. 成交量确认 (1分)
        if (indicators.volumeConfirmation) {
            score += 1.0;
        }

        // 5. MACD确认 (1分)
        if (indicators.macdBullish) {
            score += 1.0;
        }

        return score;
    }

    /**
     * 计算回调比例
     */
    private double calculateRetraceRatio(IndicatorValues indicators) {
        if (indicators.recentHigh <= indicators.recentLow) {
            return 0.0;
        }

        double totalRange = indicators.recentHigh - indicators.recentLow;
        double retraceAmount = indicators.recentHigh - indicators.currentPrice;

        return retraceAmount / totalRange;
    }

    /**
     * 生成交易信号
     */
    private SignalEvent generateTradingSignal(MarketDataEvent event, IndicatorValues indicators, double entryScore) {
        BigDecimal price = event.getKlineData().getClose();
        BigDecimal quantity = calculatePositionSize(price);

        // 计算信号置信度
        double confidence = Math.min(entryScore / 10.0, 1.0); // 最大10分，转换为0-1

        // 确定信号类型（目前只做多）
        SignalEvent.SignalType signalType = SignalEvent.SignalType.BUY;

        // 创建信号数据
        Map<String, Object> signalData = new HashMap<>();
        signalData.put("entryScore", entryScore);
        signalData.put("emaFast", indicators.emaFast);
        signalData.put("emaMid", indicators.emaMid);
        signalData.put("emaSlow", indicators.emaSlow);
        signalData.put("rsi", indicators.rsi);
        signalData.put("atr", indicators.atr);
        signalData.put("retraceRatio", calculateRetraceRatio(indicators));

        // 计算止损止盈价格
        BigDecimal stopLoss = price.subtract(BigDecimal.valueOf(indicators.atr * stopLossMultiplier));
        BigDecimal takeProfit1 = price.add(BigDecimal.valueOf(indicators.atr * takeProfitMultiplier1));
        BigDecimal takeProfit2 = price.add(BigDecimal.valueOf(indicators.atr * takeProfitMultiplier2));

        signalData.put("stopLoss", stopLoss);
        signalData.put("takeProfit1", takeProfit1);
        signalData.put("takeProfit2", takeProfit2);

        return new SignalEvent(
            event.getSymbol(),
            signalType,
            price,
            quantity,
            getStrategyName(),
            confidence,
            event.getTimestamp(),
            signalData
        );
    }

    /**
     * 计算仓位大小
     */
    private BigDecimal calculatePositionSize(BigDecimal price) {
        double positionValue = config.getInitialCapital() * config.getPositionSizeRatio();
        return BigDecimal.valueOf(positionValue).divide(price, 8, BigDecimal.ROUND_DOWN);
    }

    /**
     * 更新参数映射
     */
    private void updateParametersMap() {
        parameters.clear();
        parameters.put("emaFast", emaFast);
        parameters.put("emaMid", emaMid);
        parameters.put("emaSlow", emaSlow);
        parameters.put("rsiPeriod", rsiPeriod);
        parameters.put("rsiOversold", rsiOversold);
        parameters.put("rsiRising", rsiRising);
        parameters.put("entryScoreThreshold", entryScoreThreshold);
        parameters.put("atrPeriod", atrPeriod);
        parameters.put("stopLossMultiplier", stopLossMultiplier);
        parameters.put("takeProfitMultiplier1", takeProfitMultiplier1);
        parameters.put("takeProfitMultiplier2", takeProfitMultiplier2);
        parameters.put("retraceDepth", retraceDepth);
    }

    /**
     * 策略上下文
     * 保存每个交易对的策略状态和历史数据
     */
    private class StrategyContext {
        private final String symbol;
        private final List<KLineEntity> klineHistory = new ArrayList<>();
        private final int maxDataPoints = 200; // 保留最近200个数据点

        // 缓存的指标值
        private IndicatorValues lastIndicators;
        private long lastCalculationTime = 0;

        public StrategyContext(String symbol) {
            this.symbol = symbol;
        }

        public void updateMarketData(MarketDataEvent event) {
            klineHistory.add(0, event.getKlineData()); // 最新数据在前

            // 保持数据点数量在限制范围内
            if (klineHistory.size() > maxDataPoints) {
                klineHistory.remove(klineHistory.size() - 1);
            }

            // 清除缓存的指标值
            lastIndicators = null;
        }

        public boolean hasEnoughData() {
            return klineHistory.size() >= Math.max(emaSlow, rsiPeriod) + 10; // 确保有足够数据
        }

        public IndicatorValues calculateIndicators() {
            // 检查缓存
            long currentTime = System.currentTimeMillis();
            if (lastIndicators != null && (currentTime - lastCalculationTime) < 1000) {
                return lastIndicators;
            }

            if (!hasEnoughData()) {
                return new IndicatorValues();
            }

            // 提取价格数据
            List<BigDecimal> closes = new ArrayList<>();
            List<BigDecimal> highs = new ArrayList<>();
            List<BigDecimal> lows = new ArrayList<>();
            List<BigDecimal> volumes = new ArrayList<>();

            for (KLineEntity kline : klineHistory) {
                closes.add(kline.getClose());
                highs.add(kline.getHigh());
                lows.add(kline.getLow());
                volumes.add(BigDecimal.valueOf(kline.getVolume()));
            }

            // 计算技术指标
            IndicatorValues indicators = new IndicatorValues();

            // EMA指标
            indicators.emaFast = TechnicalIndicators.calculateEMA(closes, emaFast);
            indicators.emaMid = TechnicalIndicators.calculateEMA(closes, emaMid);
            indicators.emaSlow = TechnicalIndicators.calculateEMA(closes, emaSlow);

            // RSI指标
            indicators.rsi = TechnicalIndicators.calculateRSI(closes, rsiPeriod);

            // ATR指标
            indicators.atr = TechnicalIndicators.calculateATR(highs, lows, closes, atrPeriod);

            // MACD指标
            double[] macd = TechnicalIndicators.calculateMACD(closes);
            indicators.macdLine = macd[0];
            indicators.macdSignal = macd[1];
            indicators.macdHistogram = macd[2];
            indicators.macdBullish = macd[0] > macd[1] && macd[2] > 0;

            // 当前价格
            indicators.currentPrice = closes.get(0).doubleValue();

            // 计算近期高低点
            calculateRecentHighLow(indicators, closes, highs, lows);

            // 成交量确认
            indicators.volumeConfirmation = checkVolumeConfirmation(volumes);

            // 缓存结果
            lastIndicators = indicators;
            lastCalculationTime = currentTime;

            return indicators;
        }

        private void calculateRecentHighLow(IndicatorValues indicators, List<BigDecimal> closes,
                                            List<BigDecimal> highs, List<BigDecimal> lows) {
            int lookbackPeriod = Math.min(20, closes.size()); // 最近20根K线

            double maxHigh = Double.MIN_VALUE;
            double minLow = Double.MAX_VALUE;

            for (int i = 0; i < lookbackPeriod; i++) {
                double high = highs.get(i).doubleValue();
                double low = lows.get(i).doubleValue();

                if (high > maxHigh) {
                    maxHigh = high;
                }
                if (low < minLow) {
                    minLow = low;
                }
            }

            indicators.recentHigh = maxHigh;
            indicators.recentLow = minLow;
        }

        private boolean checkVolumeConfirmation(List<BigDecimal> volumes) {
            if (volumes.size() < 5) {
                return false;
            }

            // 检查最近的成交量是否高于平均水平
            double recentVolume = volumes.get(0).doubleValue();
            double avgVolume = 0.0;

            for (int i = 1; i < Math.min(5, volumes.size()); i++) {
                avgVolume += volumes.get(i).doubleValue();
            }
            avgVolume /= Math.min(4, volumes.size() - 1);

            return recentVolume > avgVolume * 1.2; // 成交量放大20%
        }
    }

    /**
     * 技术指标值容器
     */
    private static class IndicatorValues {
        double emaFast = 0.0;
        double emaMid = 0.0;
        double emaSlow = 0.0;
        double rsi = 50.0;
        double atr = 0.0;
        double macdLine = 0.0;
        double macdSignal = 0.0;
        double macdHistogram = 0.0;
        boolean macdBullish = false;
        double currentPrice = 0.0;
        double recentHigh = 0.0;
        double recentLow = 0.0;
        boolean volumeConfirmation = false;

        public boolean isEMABullishAlignment() {
            return emaFast > emaMid && emaMid > emaSlow && emaFast > 0 && emaMid > 0 && emaSlow > 0;
        }
    }
}
