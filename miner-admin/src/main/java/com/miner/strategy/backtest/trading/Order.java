package com.miner.strategy.backtest.trading;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易订单模型
 *
 * <AUTHOR>
 */
@Data
@Builder
public class Order {

    // 订单ID
    private String orderId;

    // 账户ID
    private String accountId;

    // 交易对
    private String symbol;

    // 订单类型
    private OrderType orderType;

    // 交易方向
    private OrderSide side;

    // 订单数量
    private BigDecimal quantity;

    // 订单价格（限价单使用）
    private BigDecimal price;

    // 已成交数量
    private BigDecimal filledQuantity;

    // 平均成交价格
    private BigDecimal avgFillPrice;

    // 订单状态
    private OrderStatus status;

    // 杠杆倍数
    private int leverage;

    // 止损价格
    private BigDecimal stopLossPrice;

    // 止盈价格
    private BigDecimal takeProfitPrice;

    // 订单创建时间
    private LocalDateTime createTime;

    // 订单更新时间
    private LocalDateTime updateTime;

    // 订单完成时间
    private LocalDateTime completeTime;

    // 手续费
    private BigDecimal fee;

    // 手续费率
    private BigDecimal feeRate;

    // 滑点
    private BigDecimal slippage;

    // 备注
    private String remark;

    /**
     * 订单类型枚举
     */
    public enum OrderType {
        MARKET,     // 市价单
        LIMIT,      // 限价单
        STOP,       // 止损单
        STOP_LIMIT  // 止损限价单
    }

    /**
     * 交易方向枚举
     */
    public enum OrderSide {
        BUY,        // 买入/做多
        SELL        // 卖出/做空
    }

    /**
     * 订单状态枚举
     */
    public enum OrderStatus {
        PENDING,        // 待处理
        PARTIAL_FILLED, // 部分成交
        FILLED,         // 完全成交
        CANCELLED,      // 已取消
        REJECTED,       // 已拒绝
        EXPIRED         // 已过期
    }

    /**
     * 计算剩余数量
     *
     * @return 剩余数量
     */
    public BigDecimal getRemainingQuantity() {
        return quantity.subtract(filledQuantity != null ? filledQuantity : BigDecimal.ZERO);
    }

    /**
     * 检查订单是否完全成交
     *
     * @return 是否完全成交
     */
    public boolean isFullyFilled() {
        return filledQuantity != null && filledQuantity.compareTo(quantity) >= 0;
    }

    /**
     * 检查订单是否部分成交
     *
     * @return 是否部分成交
     */
    public boolean isPartiallyFilled() {
        return filledQuantity != null &&
            filledQuantity.compareTo(BigDecimal.ZERO) > 0 &&
            filledQuantity.compareTo(quantity) < 0;
    }

    /**
     * 检查订单是否可以取消
     *
     * @return 是否可以取消
     */
    public boolean isCancellable() {
        return status == OrderStatus.PENDING || status == OrderStatus.PARTIAL_FILLED;
    }

    /**
     * 检查订单是否为买单
     *
     * @return 是否为买单
     */
    public boolean isBuyOrder() {
        return side == OrderSide.BUY;
    }

    /**
     * 检查订单是否为卖单
     *
     * @return 是否为卖单
     */
    public boolean isSellOrder() {
        return side == OrderSide.SELL;
    }

    /**
     * 检查是否为市价单
     *
     * @return 是否为市价单
     */
    public boolean isMarketOrder() {
        return orderType == OrderType.MARKET;
    }

    /**
     * 检查是否为限价单
     *
     * @return 是否为限价单
     */
    public boolean isLimitOrder() {
        return orderType == OrderType.LIMIT;
    }

    /**
     * 计算订单价值
     *
     * @return 订单价值
     */
    public BigDecimal getOrderValue() {
        if (price == null || quantity == null) {
            return BigDecimal.ZERO;
        }
        return price.multiply(quantity);
    }

    /**
     * 计算已成交价值
     *
     * @return 已成交价值
     */
    public BigDecimal getFilledValue() {
        if (avgFillPrice == null || filledQuantity == null) {
            return BigDecimal.ZERO;
        }
        return avgFillPrice.multiply(filledQuantity);
    }

    /**
     * 计算所需保证金
     *
     * @return 所需保证金
     */
    public BigDecimal getRequiredMargin() {
        BigDecimal orderValue = getOrderValue();
        if (orderValue.compareTo(BigDecimal.ZERO) == 0 || leverage <= 0) {
            return BigDecimal.ZERO;
        }
        return orderValue.divide(BigDecimal.valueOf(leverage), 8, BigDecimal.ROUND_UP);
    }

    /**
     * 更新成交信息
     *
     * @param fillQuantity 成交数量
     * @param fillPrice    成交价格
     */
    public void updateFill(BigDecimal fillQuantity, BigDecimal fillPrice) {
        if (filledQuantity == null) {
            filledQuantity = BigDecimal.ZERO;
        }

        // 计算新的平均成交价格
        BigDecimal totalFilledValue = getFilledValue().add(fillQuantity.multiply(fillPrice));
        BigDecimal newFilledQuantity = filledQuantity.add(fillQuantity);

        this.filledQuantity = newFilledQuantity;
        this.avgFillPrice = totalFilledValue.divide(newFilledQuantity, 8, BigDecimal.ROUND_HALF_UP);
        this.updateTime = LocalDateTime.now();

        // 更新订单状态
        if (isFullyFilled()) {
            this.status = OrderStatus.FILLED;
            this.completeTime = LocalDateTime.now();
        } else {
            this.status = OrderStatus.PARTIAL_FILLED;
        }
    }

    /**
     * 取消订单
     *
     * @param reason 取消原因
     */
    public void cancel(String reason) {
        if (isCancellable()) {
            this.status = OrderStatus.CANCELLED;
            this.remark = reason;
            this.updateTime = LocalDateTime.now();
            this.completeTime = LocalDateTime.now();
        }
    }

    /**
     * 拒绝订单
     *
     * @param reason 拒绝原因
     */
    public void reject(String reason) {
        this.status = OrderStatus.REJECTED;
        this.remark = reason;
        this.updateTime = LocalDateTime.now();
        this.completeTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return String.format("Order[%s]: %s %s %s@%s, 状态=%s, 成交=%s/%s",
            orderId, symbol, side, quantity, price, status,
            filledQuantity != null ? filledQuantity : "0", quantity);
    }
}
