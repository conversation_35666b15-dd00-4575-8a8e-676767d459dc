package com.miner.strategy.backtest.trading;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

/**
 * 交易持仓模型
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class Position {
    
    // 持仓ID
    private String positionId;
    
    // 账户ID
    private String accountId;
    
    // 交易对
    private String symbol;
    
    // 持仓方向
    private PositionSide side;
    
    // 持仓数量
    private BigDecimal quantity;
    
    // 平均开仓价格
    private BigDecimal avgEntryPrice;
    
    // 当前市场价格
    private BigDecimal currentPrice;
    
    // 杠杆倍数
    private int leverage;
    
    // 保证金
    private BigDecimal margin;
    
    // 未实现盈亏
    private BigDecimal unrealizedPnl;
    
    // 已实现盈亏
    private BigDecimal realizedPnl;
    
    // 止损价格
    private BigDecimal stopLossPrice;
    
    // 止盈价格
    private BigDecimal takeProfitPrice;
    
    // 持仓状态
    private PositionStatus status;
    
    // 开仓时间
    private LocalDateTime openTime;
    
    // 最后更新时间
    private LocalDateTime updateTime;
    
    // 平仓时间
    private LocalDateTime closeTime;
    
    // 累计手续费
    private BigDecimal totalFee;
    
    // 最高价格（用于追踪止损）
    private BigDecimal highestPrice;
    
    // 最低价格（用于追踪止损）
    private BigDecimal lowestPrice;
    
    /**
     * 持仓方向枚举
     */
    public enum PositionSide {
        LONG,   // 多头持仓
        SHORT   // 空头持仓
    }
    
    /**
     * 持仓状态枚举
     */
    public enum PositionStatus {
        OPEN,       // 开仓状态
        CLOSING,    // 平仓中
        CLOSED      // 已平仓
    }
    
    /**
     * 检查是否为多头持仓
     * 
     * @return 是否为多头
     */
    public boolean isLongPosition() {
        return side == PositionSide.LONG;
    }
    
    /**
     * 检查是否为空头持仓
     * 
     * @return 是否为空头
     */
    public boolean isShortPosition() {
        return side == PositionSide.SHORT;
    }
    
    /**
     * 更新当前价格并重新计算盈亏
     * 
     * @param newPrice 新价格
     */
    public void updatePrice(BigDecimal newPrice) {
        this.currentPrice = newPrice;
        this.updateTime = LocalDateTime.now();
        
        // 更新最高/最低价格
        if (highestPrice == null || newPrice.compareTo(highestPrice) > 0) {
            highestPrice = newPrice;
        }
        if (lowestPrice == null || newPrice.compareTo(lowestPrice) < 0) {
            lowestPrice = newPrice;
        }
        
        // 重新计算未实现盈亏
        calculateUnrealizedPnl();
    }
    
    /**
     * 计算未实现盈亏
     */
    public void calculateUnrealizedPnl() {
        if (currentPrice == null || avgEntryPrice == null || quantity == null) {
            unrealizedPnl = BigDecimal.ZERO;
            return;
        }
        
        BigDecimal priceDiff;
        if (isLongPosition()) {
            // 多头：当前价格 - 开仓价格
            priceDiff = currentPrice.subtract(avgEntryPrice);
        } else {
            // 空头：开仓价格 - 当前价格
            priceDiff = avgEntryPrice.subtract(currentPrice);
        }
        
        unrealizedPnl = priceDiff.multiply(quantity);
    }
    
    /**
     * 计算收益率
     * 
     * @return 收益率
     */
    public BigDecimal getReturnRate() {
        if (margin == null || margin.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal totalPnl = unrealizedPnl.add(realizedPnl != null ? realizedPnl : BigDecimal.ZERO);
        return totalPnl.divide(margin, 6, RoundingMode.HALF_UP);
    }
    
    /**
     * 计算持仓价值
     * 
     * @return 持仓价值
     */
    public BigDecimal getPositionValue() {
        if (currentPrice == null || quantity == null) {
            return BigDecimal.ZERO;
        }
        return currentPrice.multiply(quantity);
    }
    
    /**
     * 检查是否触发止损
     * 
     * @return 是否触发止损
     */
    public boolean isStopLossTriggered() {
        if (stopLossPrice == null || currentPrice == null) {
            return false;
        }
        
        if (isLongPosition()) {
            // 多头：当前价格 <= 止损价格
            return currentPrice.compareTo(stopLossPrice) <= 0;
        } else {
            // 空头：当前价格 >= 止损价格
            return currentPrice.compareTo(stopLossPrice) >= 0;
        }
    }
    
    /**
     * 检查是否触发止盈
     * 
     * @return 是否触发止盈
     */
    public boolean isTakeProfitTriggered() {
        if (takeProfitPrice == null || currentPrice == null) {
            return false;
        }
        
        if (isLongPosition()) {
            // 多头：当前价格 >= 止盈价格
            return currentPrice.compareTo(takeProfitPrice) >= 0;
        } else {
            // 空头：当前价格 <= 止盈价格
            return currentPrice.compareTo(takeProfitPrice) <= 0;
        }
    }
    
    /**
     * 部分平仓
     * 
     * @param closeQuantity 平仓数量
     * @param closePrice 平仓价格
     * @param fee 手续费
     * @return 实现的盈亏
     */
    public BigDecimal partialClose(BigDecimal closeQuantity, BigDecimal closePrice, BigDecimal fee) {
        if (closeQuantity.compareTo(quantity) > 0) {
            throw new IllegalArgumentException("平仓数量不能超过持仓数量");
        }
        
        // 计算这部分持仓的盈亏
        BigDecimal priceDiff;
        if (isLongPosition()) {
            priceDiff = closePrice.subtract(avgEntryPrice);
        } else {
            priceDiff = avgEntryPrice.subtract(closePrice);
        }
        
        BigDecimal partialPnl = priceDiff.multiply(closeQuantity).subtract(fee);
        
        // 更新持仓数量和保证金
        BigDecimal closeRatio = closeQuantity.divide(quantity, 8, RoundingMode.HALF_UP);
        quantity = quantity.subtract(closeQuantity);
        margin = margin.multiply(BigDecimal.ONE.subtract(closeRatio));
        
        // 累计已实现盈亏和手续费
        realizedPnl = (realizedPnl != null ? realizedPnl : BigDecimal.ZERO).add(partialPnl);
        totalFee = (totalFee != null ? totalFee : BigDecimal.ZERO).add(fee);
        
        updateTime = LocalDateTime.now();
        
        // 如果完全平仓，更新状态
        if (quantity.compareTo(BigDecimal.ZERO) == 0) {
            status = PositionStatus.CLOSED;
            closeTime = LocalDateTime.now();
        }
        
        return partialPnl;
    }
    
    /**
     * 完全平仓
     * 
     * @param closePrice 平仓价格
     * @param fee 手续费
     * @return 实现的盈亏
     */
    public BigDecimal fullClose(BigDecimal closePrice, BigDecimal fee) {
        return partialClose(quantity, closePrice, fee);
    }
    
    /**
     * 增加持仓（加仓）
     * 
     * @param addQuantity 增加数量
     * @param addPrice 增加价格
     * @param addMargin 增加保证金
     */
    public void addPosition(BigDecimal addQuantity, BigDecimal addPrice, BigDecimal addMargin) {
        // 计算新的平均开仓价格
        BigDecimal totalValue = avgEntryPrice.multiply(quantity).add(addPrice.multiply(addQuantity));
        BigDecimal newQuantity = quantity.add(addQuantity);
        
        this.avgEntryPrice = totalValue.divide(newQuantity, 8, RoundingMode.HALF_UP);
        this.quantity = newQuantity;
        this.margin = margin.add(addMargin);
        this.updateTime = LocalDateTime.now();
        
        // 重新计算未实现盈亏
        calculateUnrealizedPnl();
    }
    
    /**
     * 获取持仓摘要
     * 
     * @return 持仓摘要
     */
    public String getSummary() {
        return String.format(
                "持仓[%s]: %s %s %.4f@%.2f, 当前价格=%.2f, 未实现盈亏=%.2f, 收益率=%.2f%%, 保证金=%.2f",
                positionId, symbol, side, quantity, avgEntryPrice, currentPrice, 
                unrealizedPnl, getReturnRate().multiply(BigDecimal.valueOf(100)), margin
        );
    }
}
