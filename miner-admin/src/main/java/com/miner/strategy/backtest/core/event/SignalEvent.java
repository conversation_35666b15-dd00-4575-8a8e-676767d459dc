package com.miner.strategy.backtest.core.event;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 信号事件
 * 包含交易信号信息
 * 
 * <AUTHOR>
 */
public class SignalEvent extends Event {
    
    private final String symbol;
    private final SignalType signalType;
    private final BigDecimal price;
    private final BigDecimal quantity;
    private final String strategyName;
    private final double confidence;
    private final Map<String, Object> signalData;
    
    public SignalEvent(String symbol, SignalType signalType, BigDecimal price, 
                      BigDecimal quantity, String strategyName, double confidence,
                      long timestamp, Map<String, Object> signalData) {
        super(EventType.SIGNAL, timestamp);
        this.symbol = symbol;
        this.signalType = signalType;
        this.price = price;
        this.quantity = quantity;
        this.strategyName = strategyName;
        this.confidence = confidence;
        this.signalData = signalData;
    }
    
    public SignalEvent(String symbol, SignalType signalType, BigDecimal price, 
                      BigDecimal quantity, String strategyName, double confidence, long timestamp) {
        this(symbol, signalType, price, quantity, strategyName, confidence, timestamp, null);
    }
    
    public String getSymbol() {
        return symbol;
    }
    
    public SignalType getSignalType() {
        return signalType;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public BigDecimal getQuantity() {
        return quantity;
    }
    
    public String getStrategyName() {
        return strategyName;
    }
    
    public double getConfidence() {
        return confidence;
    }
    
    public Map<String, Object> getSignalData() {
        return signalData;
    }
    
    /**
     * 获取信号数据
     * 
     * @param key 键
     * @param <T> 值类型
     * @return 值
     */
    @SuppressWarnings("unchecked")
    public <T> T getSignalData(String key) {
        if (signalData == null) {
            return null;
        }
        return (T) signalData.get(key);
    }
    
    /**
     * 信号类型枚举
     */
    public enum SignalType {
        BUY,            // 买入信号
        SELL,           // 卖出信号
        CLOSE_LONG,     // 平多信号
        CLOSE_SHORT,    // 平空信号
        STOP_LOSS,      // 止损信号
        TAKE_PROFIT,    // 止盈信号
        CANCEL          // 取消信号
    }
    
    @Override
    public String toString() {
        return String.format("SignalEvent{symbol='%s', type=%s, price=%s, quantity=%s, strategy='%s', confidence=%.2f}", 
                           symbol, signalType, price, quantity, strategyName, confidence);
    }
}
