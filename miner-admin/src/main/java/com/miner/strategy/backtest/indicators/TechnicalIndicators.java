package com.miner.strategy.backtest.indicators;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 技术指标计算库
 * 为回测框架提供高精度的技术指标计算
 * 
 * <AUTHOR>
 */
@Slf4j
public class TechnicalIndicators {
    
    // 指标缓存
    private static final Map<String, IndicatorCache> cache = new ConcurrentHashMap<>();
    private static final long CACHE_EXPIRE_TIME = 60000; // 1分钟缓存
    
    /**
     * 计算EMA指标
     * 
     * @param prices 价格列表（最新价格在前）
     * @param period 周期
     * @return EMA值
     */
    public static double calculateEMA(List<BigDecimal> prices, int period) {
        if (prices == null || prices.size() < period) {
            return 0.0;
        }
        
        String cacheKey = generateCacheKey("EMA", prices, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }
        
        double multiplier = 2.0 / (period + 1);
        double ema = prices.get(prices.size() - 1).doubleValue(); // 从最老的价格开始
        
        // 从倒数第二个价格开始计算
        for (int i = prices.size() - 2; i >= 0; i--) {
            double price = prices.get(i).doubleValue();
            ema = (price * multiplier) + (ema * (1 - multiplier));
        }
        
        cache.put(cacheKey, new IndicatorCache(ema, System.currentTimeMillis()));
        return ema;
    }
    
    /**
     * 计算SMA指标
     * 
     * @param prices 价格列表
     * @param period 周期
     * @return SMA值
     */
    public static double calculateSMA(List<BigDecimal> prices, int period) {
        if (prices == null || prices.size() < period) {
            return 0.0;
        }
        
        String cacheKey = generateCacheKey("SMA", prices, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }
        
        double sum = 0.0;
        for (int i = 0; i < period; i++) {
            sum += prices.get(i).doubleValue();
        }
        
        double sma = sum / period;
        cache.put(cacheKey, new IndicatorCache(sma, System.currentTimeMillis()));
        return sma;
    }
    
    /**
     * 计算RSI指标
     * 
     * @param prices 价格列表
     * @param period 周期
     * @return RSI值
     */
    public static double calculateRSI(List<BigDecimal> prices, int period) {
        if (prices == null || prices.size() < period + 1) {
            return 50.0; // 默认中性值
        }
        
        String cacheKey = generateCacheKey("RSI", prices, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }
        
        double gainSum = 0.0;
        double lossSum = 0.0;
        
        // 计算价格变化
        for (int i = 0; i < period; i++) {
            double change = prices.get(i).doubleValue() - prices.get(i + 1).doubleValue();
            if (change > 0) {
                gainSum += change;
            } else {
                lossSum += Math.abs(change);
            }
        }
        
        double avgGain = gainSum / period;
        double avgLoss = lossSum / period;
        
        double rsi = 50.0; // 默认值
        if (avgLoss != 0) {
            double rs = avgGain / avgLoss;
            rsi = 100.0 - (100.0 / (1.0 + rs));
        }
        
        cache.put(cacheKey, new IndicatorCache(rsi, System.currentTimeMillis()));
        return rsi;
    }
    
    /**
     * 计算MACD指标
     * 
     * @param prices 价格列表
     * @return MACD数组 [MACD线, 信号线, 柱状图]
     */
    public static double[] calculateMACD(List<BigDecimal> prices) {
        return calculateMACD(prices, 12, 26, 9);
    }
    
    /**
     * 计算MACD指标（自定义参数）
     * 
     * @param prices 价格列表
     * @param fastPeriod 快线周期
     * @param slowPeriod 慢线周期
     * @param signalPeriod 信号线周期
     * @return MACD数组 [MACD线, 信号线, 柱状图]
     */
    public static double[] calculateMACD(List<BigDecimal> prices, int fastPeriod, int slowPeriod, int signalPeriod) {
        if (prices == null || prices.size() < slowPeriod + signalPeriod) {
            return new double[]{0.0, 0.0, 0.0};
        }
        
        String cacheKey = generateCacheKey("MACD", prices, fastPeriod, slowPeriod, signalPeriod);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return (double[]) cached.getRawValue();
        }
        
        // 计算快慢EMA
        double fastEMA = calculateEMA(prices, fastPeriod);
        double slowEMA = calculateEMA(prices, slowPeriod);
        
        // MACD线 = 快EMA - 慢EMA
        double macdLine = fastEMA - slowEMA;
        
        // 计算信号线（MACD线的EMA）
        List<BigDecimal> macdValues = new ArrayList<>();
        macdValues.add(BigDecimal.valueOf(macdLine));
        
        // 这里简化处理，实际应该计算历史MACD值的EMA
        double signalLine = macdLine * 0.9; // 简化计算
        
        // 柱状图 = MACD线 - 信号线
        double histogram = macdLine - signalLine;
        
        double[] result = {macdLine, signalLine, histogram};
        cache.put(cacheKey, new IndicatorCache(0.0, System.currentTimeMillis(), result));
        return result;
    }
    
    /**
     * 计算ATR指标
     * 
     * @param highs 最高价列表
     * @param lows 最低价列表
     * @param closes 收盘价列表
     * @param period 周期
     * @return ATR值
     */
    public static double calculateATR(List<BigDecimal> highs, List<BigDecimal> lows, 
                                    List<BigDecimal> closes, int period) {
        if (highs == null || lows == null || closes == null || 
            highs.size() < period + 1 || lows.size() < period + 1 || closes.size() < period + 1) {
            return 0.0;
        }
        
        String cacheKey = generateCacheKey("ATR", closes, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }
        
        double trSum = 0.0;
        
        for (int i = 0; i < period; i++) {
            double high = highs.get(i).doubleValue();
            double low = lows.get(i).doubleValue();
            double prevClose = closes.get(i + 1).doubleValue();
            
            // True Range = max(high-low, |high-prevClose|, |low-prevClose|)
            double tr1 = high - low;
            double tr2 = Math.abs(high - prevClose);
            double tr3 = Math.abs(low - prevClose);
            
            double tr = Math.max(tr1, Math.max(tr2, tr3));
            trSum += tr;
        }
        
        double atr = trSum / period;
        cache.put(cacheKey, new IndicatorCache(atr, System.currentTimeMillis()));
        return atr;
    }
    
    /**
     * 计算布林带
     * 
     * @param prices 价格列表
     * @param period 周期
     * @param stdDevMultiplier 标准差倍数
     * @return 布林带数组 [上轨, 中轨, 下轨]
     */
    public static double[] calculateBollingerBands(List<BigDecimal> prices, int period, double stdDevMultiplier) {
        if (prices == null || prices.size() < period) {
            return new double[]{0.0, 0.0, 0.0};
        }
        
        String cacheKey = generateCacheKey("BB", prices, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return (double[]) cached.getRawValue();
        }
        
        // 计算中轨（SMA）
        double sma = calculateSMA(prices, period);
        
        // 计算标准差
        double variance = 0.0;
        for (int i = 0; i < period; i++) {
            double diff = prices.get(i).doubleValue() - sma;
            variance += diff * diff;
        }
        double stdDev = Math.sqrt(variance / period);
        
        // 计算上下轨
        double upperBand = sma + (stdDev * stdDevMultiplier);
        double lowerBand = sma - (stdDev * stdDevMultiplier);
        
        double[] result = {upperBand, sma, lowerBand};
        cache.put(cacheKey, new IndicatorCache(0.0, System.currentTimeMillis(), result));
        return result;
    }
    
    /**
     * 计算价格斜率
     * 
     * @param prices 价格列表
     * @param period 计算周期
     * @return 斜率百分比
     */
    public static double calculateSlope(List<BigDecimal> prices, int period) {
        if (prices == null || prices.size() < period) {
            return 0.0;
        }
        
        double startPrice = prices.get(period - 1).doubleValue();
        double endPrice = prices.get(0).doubleValue();
        
        if (startPrice == 0) {
            return 0.0;
        }
        
        return ((endPrice - startPrice) / startPrice) * 100.0 / period;
    }
    
    /**
     * 检查EMA多头排列
     * 
     * @param prices 价格列表
     * @param fastPeriod 快线周期
     * @param midPeriod 中线周期
     * @param slowPeriod 慢线周期
     * @return 是否多头排列
     */
    public static boolean isEMABullishAlignment(List<BigDecimal> prices, int fastPeriod, int midPeriod, int slowPeriod) {
        if (prices == null || prices.size() < slowPeriod) {
            return false;
        }
        
        double emaFast = calculateEMA(prices, fastPeriod);
        double emaMid = calculateEMA(prices, midPeriod);
        double emaSlow = calculateEMA(prices, slowPeriod);
        
        return emaFast > emaMid && emaMid > emaSlow;
    }
    
    /**
     * 生成缓存键
     */
    private static String generateCacheKey(String indicator, List<BigDecimal> prices, int... params) {
        StringBuilder key = new StringBuilder(indicator);
        key.append("_").append(prices.size());
        if (prices.size() > 0) {
            key.append("_").append(prices.get(0).toString());
        }
        for (int param : params) {
            key.append("_").append(param);
        }
        return key.toString();
    }
    
    /**
     * 清理过期缓存
     */
    public static void cleanExpiredCache() {
        long currentTime = System.currentTimeMillis();
        cache.entrySet().removeIf(entry -> entry.getValue().isExpired(currentTime));
    }
    
    /**
     * 指标缓存类
     */
    private static class IndicatorCache {
        private final double value;
        private final long timestamp;
        private final Object rawValue;
        
        public IndicatorCache(double value, long timestamp) {
            this(value, timestamp, null);
        }
        
        public IndicatorCache(double value, long timestamp, Object rawValue) {
            this.value = value;
            this.timestamp = timestamp;
            this.rawValue = rawValue;
        }
        
        public double getValue() {
            return value;
        }
        
        public Object getRawValue() {
            return rawValue != null ? rawValue : value;
        }
        
        public boolean isExpired() {
            return isExpired(System.currentTimeMillis());
        }
        
        public boolean isExpired(long currentTime) {
            return (currentTime - timestamp) > CACHE_EXPIRE_TIME;
        }
    }
}
