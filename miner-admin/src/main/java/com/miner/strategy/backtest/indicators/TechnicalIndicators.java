package com.miner.strategy.backtest.indicators;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 技术指标计算库
 * 为回测框架提供高精度的技术指标计算
 *
 * <AUTHOR>
 */
@Slf4j
public class TechnicalIndicators {

    // 指标缓存
    private static final Map<String, IndicatorCache> cache = new ConcurrentHashMap<>();
    private static final long CACHE_EXPIRE_TIME = 60000; // 1分钟缓存

    /**
     * 计算EMA指标
     *
     * @param prices 价格列表（最新价格在前）
     * @param period 周期
     * @return EMA值
     */
    public static double calculateEMA(List<BigDecimal> prices, int period) {
        if (prices == null || prices.size() < period) {
            return 0.0;
        }

        String cacheKey = generateCacheKey("EMA", prices, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        double multiplier = 2.0 / (period + 1);
        double ema = prices.get(prices.size() - 1).doubleValue(); // 从最老的价格开始

        // 从倒数第二个价格开始计算
        for (int i = prices.size() - 2; i >= 0; i--) {
            double price = prices.get(i).doubleValue();
            ema = (price * multiplier) + (ema * (1 - multiplier));
        }

        cache.put(cacheKey, new IndicatorCache(ema, System.currentTimeMillis()));
        return ema;
    }

    /**
     * 计算SMA指标
     *
     * @param prices 价格列表
     * @param period 周期
     * @return SMA值
     */
    public static double calculateSMA(List<BigDecimal> prices, int period) {
        if (prices == null || prices.size() < period) {
            return 0.0;
        }

        String cacheKey = generateCacheKey("SMA", prices, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        double sum = 0.0;
        for (int i = 0; i < period; i++) {
            sum += prices.get(i).doubleValue();
        }

        double sma = sum / period;
        cache.put(cacheKey, new IndicatorCache(sma, System.currentTimeMillis()));
        return sma;
    }

    /**
     * 计算RSI指标
     *
     * @param prices 价格列表
     * @param period 周期
     * @return RSI值
     */
    public static double calculateRSI(List<BigDecimal> prices, int period) {
        if (prices == null || prices.size() < period + 1) {
            return 50.0; // 默认中性值
        }

        String cacheKey = generateCacheKey("RSI", prices, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        double gainSum = 0.0;
        double lossSum = 0.0;

        // 计算价格变化
        for (int i = 0; i < period; i++) {
            double change = prices.get(i).doubleValue() - prices.get(i + 1).doubleValue();
            if (change > 0) {
                gainSum += change;
            } else {
                lossSum += Math.abs(change);
            }
        }

        double avgGain = gainSum / period;
        double avgLoss = lossSum / period;

        double rsi = 50.0; // 默认值
        if (avgLoss != 0) {
            double rs = avgGain / avgLoss;
            rsi = 100.0 - (100.0 / (1.0 + rs));
        }

        cache.put(cacheKey, new IndicatorCache(rsi, System.currentTimeMillis()));
        return rsi;
    }

    /**
     * 计算MACD指标
     *
     * @param prices 价格列表
     * @return MACD数组 [MACD线, 信号线, 柱状图]
     */
    public static double[] calculateMACD(List<BigDecimal> prices) {
        return calculateMACD(prices, 12, 26, 9);
    }

    /**
     * 计算MACD指标（自定义参数）- 改进版本
     *
     * @param prices       价格列表
     * @param fastPeriod   快线周期
     * @param slowPeriod   慢线周期
     * @param signalPeriod 信号线周期
     * @return MACD数组 [MACD线, 信号线, 柱状图]
     */
    public static double[] calculateMACD(List<BigDecimal> prices, int fastPeriod, int slowPeriod, int signalPeriod) {
        if (prices == null || prices.size() < slowPeriod + signalPeriod) {
            return new double[]{0.0, 0.0, 0.0};
        }

        String cacheKey = generateCacheKey("MACD", prices, fastPeriod, slowPeriod, signalPeriod);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return (double[]) cached.getRawValue();
        }

        // 计算快慢EMA
        double fastEMA = calculateEMA(prices, fastPeriod);
        double slowEMA = calculateEMA(prices, slowPeriod);

        // MACD线 = 快EMA - 慢EMA
        double macdLine = fastEMA - slowEMA;

        // 计算信号线（MACD线的EMA）- 改进计算
        List<BigDecimal> macdValues = new ArrayList<>();

        // 计算历史MACD值用于信号线计算
        int requiredLength = Math.min(signalPeriod, prices.size() - slowPeriod);
        for (int i = 0; i < requiredLength; i++) {
            List<BigDecimal> subPrices = prices.subList(i, Math.min(i + slowPeriod + 1, prices.size()));
            if (subPrices.size() >= slowPeriod) {
                double subFastEMA = calculateEMA(subPrices, fastPeriod);
                double subSlowEMA = calculateEMA(subPrices, slowPeriod);
                macdValues.add(BigDecimal.valueOf(subFastEMA - subSlowEMA));
            }
        }

        // 计算信号线（MACD值的EMA）
        double signalLine = 0.0;
        if (macdValues.size() >= signalPeriod) {
            signalLine = calculateEMA(macdValues, signalPeriod);
        } else {
            signalLine = macdLine * 0.9; // 回退到简化计算
        }

        // 柱状图 = MACD线 - 信号线
        double histogram = macdLine - signalLine;

        double[] result = {macdLine, signalLine, histogram};
        cache.put(cacheKey, new IndicatorCache(0.0, System.currentTimeMillis(), result));
        return result;
    }

    /**
     * 计算ATR指标
     *
     * @param highs  最高价列表
     * @param lows   最低价列表
     * @param closes 收盘价列表
     * @param period 周期
     * @return ATR值
     */
    public static double calculateATR(List<BigDecimal> highs, List<BigDecimal> lows,
                                      List<BigDecimal> closes, int period) {
        if (highs == null || lows == null || closes == null ||
            highs.size() < period + 1 || lows.size() < period + 1 || closes.size() < period + 1) {
            return 0.0;
        }

        String cacheKey = generateCacheKey("ATR", closes, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        double trSum = 0.0;

        for (int i = 0; i < period; i++) {
            double high = highs.get(i).doubleValue();
            double low = lows.get(i).doubleValue();
            double prevClose = closes.get(i + 1).doubleValue();

            // True Range = max(high-low, |high-prevClose|, |low-prevClose|)
            double tr1 = high - low;
            double tr2 = Math.abs(high - prevClose);
            double tr3 = Math.abs(low - prevClose);

            double tr = Math.max(tr1, Math.max(tr2, tr3));
            trSum += tr;
        }

        double atr = trSum / period;
        cache.put(cacheKey, new IndicatorCache(atr, System.currentTimeMillis()));
        return atr;
    }

    /**
     * 计算布林带
     *
     * @param prices           价格列表
     * @param period           周期
     * @param stdDevMultiplier 标准差倍数
     * @return 布林带数组 [上轨, 中轨, 下轨]
     */
    public static double[] calculateBollingerBands(List<BigDecimal> prices, int period, double stdDevMultiplier) {
        if (prices == null || prices.size() < period) {
            return new double[]{0.0, 0.0, 0.0};
        }

        String cacheKey = generateCacheKey("BB", prices, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return (double[]) cached.getRawValue();
        }

        // 计算中轨（SMA）
        double sma = calculateSMA(prices, period);

        // 计算标准差
        double variance = 0.0;
        for (int i = 0; i < period; i++) {
            double diff = prices.get(i).doubleValue() - sma;
            variance += diff * diff;
        }
        double stdDev = Math.sqrt(variance / period);

        // 计算上下轨
        double upperBand = sma + (stdDev * stdDevMultiplier);
        double lowerBand = sma - (stdDev * stdDevMultiplier);

        double[] result = {upperBand, sma, lowerBand};
        cache.put(cacheKey, new IndicatorCache(0.0, System.currentTimeMillis(), result));
        return result;
    }

    /**
     * 计算价格斜率
     *
     * @param prices 价格列表
     * @param period 计算周期
     * @return 斜率百分比
     */
    public static double calculateSlope(List<BigDecimal> prices, int period) {
        if (prices == null || prices.size() < period) {
            return 0.0;
        }

        double startPrice = prices.get(period - 1).doubleValue();
        double endPrice = prices.get(0).doubleValue();

        if (startPrice == 0) {
            return 0.0;
        }

        return ((endPrice - startPrice) / startPrice) * 100.0 / period;
    }

    /**
     * 检查EMA多头排列
     *
     * @param prices     价格列表
     * @param fastPeriod 快线周期
     * @param midPeriod  中线周期
     * @param slowPeriod 慢线周期
     * @return 是否多头排列
     */
    public static boolean isEMABullishAlignment(List<BigDecimal> prices, int fastPeriod, int midPeriod, int slowPeriod) {
        if (prices == null || prices.size() < slowPeriod) {
            return false;
        }

        double emaFast = calculateEMA(prices, fastPeriod);
        double emaMid = calculateEMA(prices, midPeriod);
        double emaSlow = calculateEMA(prices, slowPeriod);

        return emaFast > emaMid && emaMid > emaSlow;
    }

    /**
     * 计算ADX指标（平均趋向指数）
     *
     * @param highs  最高价列表
     * @param lows   最低价列表
     * @param closes 收盘价列表
     * @param period 周期
     * @return ADX值
     */
    public static double calculateADX(List<BigDecimal> highs, List<BigDecimal> lows,
                                      List<BigDecimal> closes, int period) {
        if (highs == null || lows == null || closes == null ||
            highs.size() < period * 2 || lows.size() < period * 2 || closes.size() < period * 2) {
            return 0.0;
        }

        String cacheKey = generateCacheKey("ADX", closes, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        // 计算True Range和方向移动
        List<Double> trList = new ArrayList<>();
        List<Double> dmPlusList = new ArrayList<>();
        List<Double> dmMinusList = new ArrayList<>();

        for (int i = 0; i < period + 1; i++) {
            double high = highs.get(i).doubleValue();
            double low = lows.get(i).doubleValue();
            double close = closes.get(i).doubleValue();
            double prevHigh = highs.get(i + 1).doubleValue();
            double prevLow = lows.get(i + 1).doubleValue();
            double prevClose = closes.get(i + 1).doubleValue();

            // True Range
            double tr1 = high - low;
            double tr2 = Math.abs(high - prevClose);
            double tr3 = Math.abs(low - prevClose);
            double tr = Math.max(tr1, Math.max(tr2, tr3));
            trList.add(tr);

            // 方向移动
            double dmPlus = (high - prevHigh > prevLow - low) ? Math.max(high - prevHigh, 0) : 0;
            double dmMinus = (prevLow - low > high - prevHigh) ? Math.max(prevLow - low, 0) : 0;
            dmPlusList.add(dmPlus);
            dmMinusList.add(dmMinus);
        }

        // 计算平滑的TR、DM+、DM-
        double smoothedTR = trList.stream().mapToDouble(Double::doubleValue).sum();
        double smoothedDMPlus = dmPlusList.stream().mapToDouble(Double::doubleValue).sum();
        double smoothedDMMinus = dmMinusList.stream().mapToDouble(Double::doubleValue).sum();

        // 计算DI+和DI-
        double diPlus = (smoothedTR != 0) ? (smoothedDMPlus / smoothedTR) * 100 : 0;
        double diMinus = (smoothedTR != 0) ? (smoothedDMMinus / smoothedTR) * 100 : 0;

        // 计算DX
        double dx = 0;
        if (diPlus + diMinus != 0) {
            dx = Math.abs(diPlus - diMinus) / (diPlus + diMinus) * 100;
        }

        // ADX是DX的移动平均（简化计算）
        double adx = dx;

        cache.put(cacheKey, new IndicatorCache(adx, System.currentTimeMillis()));
        return adx;
    }

    /**
     * 计算随机指标（Stochastic Oscillator）
     *
     * @param highs   最高价列表
     * @param lows    最低价列表
     * @param closes  收盘价列表
     * @param kPeriod %K周期
     * @param dPeriod %D周期
     * @return 随机指标数组 [%K, %D]
     */
    public static double[] calculateStochastic(List<BigDecimal> highs, List<BigDecimal> lows,
                                               List<BigDecimal> closes, int kPeriod, int dPeriod) {
        if (highs == null || lows == null || closes == null ||
            highs.size() < kPeriod + dPeriod || lows.size() < kPeriod + dPeriod || closes.size() < kPeriod + dPeriod) {
            return new double[]{0.0, 0.0};
        }

        String cacheKey = generateCacheKey("STOCH", closes, kPeriod, dPeriod);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return (double[]) cached.getRawValue();
        }

        // 计算%K
        double highestHigh = Double.MIN_VALUE;
        double lowestLow = Double.MAX_VALUE;

        for (int i = 0; i < kPeriod; i++) {
            highestHigh = Math.max(highestHigh, highs.get(i).doubleValue());
            lowestLow = Math.min(lowestLow, lows.get(i).doubleValue());
        }

        double currentClose = closes.get(0).doubleValue();
        double stochK = 0;
        if (highestHigh != lowestLow) {
            stochK = ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100;
        }

        // 计算%D（%K的移动平均）
        List<BigDecimal> kValues = new ArrayList<>();
        for (int i = 0; i < dPeriod; i++) {
            // 简化处理，实际应该计算每个周期的%K值
            kValues.add(BigDecimal.valueOf(stochK));
        }
        double stochD = calculateSMA(kValues, dPeriod);

        double[] result = {stochK, stochD};
        cache.put(cacheKey, new IndicatorCache(0.0, System.currentTimeMillis(), result));
        return result;
    }

    /**
     * 计算威廉指标（Williams %R）
     *
     * @param highs  最高价列表
     * @param lows   最低价列表
     * @param closes 收盘价列表
     * @param period 周期
     * @return Williams %R值
     */
    public static double calculateWilliamsR(List<BigDecimal> highs, List<BigDecimal> lows,
                                            List<BigDecimal> closes, int period) {
        if (highs == null || lows == null || closes == null ||
            highs.size() < period || lows.size() < period || closes.size() < period) {
            return -50.0; // 默认中性值
        }

        String cacheKey = generateCacheKey("WR", closes, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        // 找到周期内的最高价和最低价
        double highestHigh = Double.MIN_VALUE;
        double lowestLow = Double.MAX_VALUE;

        for (int i = 0; i < period; i++) {
            highestHigh = Math.max(highestHigh, highs.get(i).doubleValue());
            lowestLow = Math.min(lowestLow, lows.get(i).doubleValue());
        }

        double currentClose = closes.get(0).doubleValue();
        double williamsR = -100.0; // 默认值

        if (highestHigh != lowestLow) {
            williamsR = ((highestHigh - currentClose) / (highestHigh - lowestLow)) * (-100);
        }

        cache.put(cacheKey, new IndicatorCache(williamsR, System.currentTimeMillis()));
        return williamsR;
    }

    /**
     * 计算CCI指标（商品通道指数）
     *
     * @param highs  最高价列表
     * @param lows   最低价列表
     * @param closes 收盘价列表
     * @param period 周期
     * @return CCI值
     */
    public static double calculateCCI(List<BigDecimal> highs, List<BigDecimal> lows,
                                      List<BigDecimal> closes, int period) {
        if (highs == null || lows == null || closes == null ||
            highs.size() < period || lows.size() < period || closes.size() < period) {
            return 0.0;
        }

        String cacheKey = generateCacheKey("CCI", closes, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        // 计算典型价格（Typical Price）
        List<BigDecimal> typicalPrices = new ArrayList<>();
        for (int i = 0; i < period; i++) {
            double tp = (highs.get(i).doubleValue() + lows.get(i).doubleValue() + closes.get(i).doubleValue()) / 3.0;
            typicalPrices.add(BigDecimal.valueOf(tp));
        }

        // 计算典型价格的移动平均
        double smaTP = calculateSMA(typicalPrices, period);

        // 计算平均偏差
        double meanDeviation = 0.0;
        for (BigDecimal tp : typicalPrices) {
            meanDeviation += Math.abs(tp.doubleValue() - smaTP);
        }
        meanDeviation /= period;

        // 计算CCI
        double currentTP = typicalPrices.get(0).doubleValue();
        double cci = 0.0;
        if (meanDeviation != 0) {
            cci = (currentTP - smaTP) / (0.015 * meanDeviation);
        }

        cache.put(cacheKey, new IndicatorCache(cci, System.currentTimeMillis()));
        return cci;
    }

    /**
     * 计算动量指标（Momentum）
     *
     * @param prices 价格列表
     * @param period 周期
     * @return 动量值
     */
    public static double calculateMomentum(List<BigDecimal> prices, int period) {
        if (prices == null || prices.size() < period + 1) {
            return 0.0;
        }

        String cacheKey = generateCacheKey("MOM", prices, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        double currentPrice = prices.get(0).doubleValue();
        double pastPrice = prices.get(period).doubleValue();

        double momentum = currentPrice - pastPrice;

        cache.put(cacheKey, new IndicatorCache(momentum, System.currentTimeMillis()));
        return momentum;
    }

    /**
     * 计算变化率指标（Rate of Change）
     *
     * @param prices 价格列表
     * @param period 周期
     * @return ROC值（百分比）
     */
    public static double calculateROC(List<BigDecimal> prices, int period) {
        if (prices == null || prices.size() < period + 1) {
            return 0.0;
        }

        String cacheKey = generateCacheKey("ROC", prices, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        double currentPrice = prices.get(0).doubleValue();
        double pastPrice = prices.get(period).doubleValue();

        double roc = 0.0;
        if (pastPrice != 0) {
            roc = ((currentPrice - pastPrice) / pastPrice) * 100.0;
        }

        cache.put(cacheKey, new IndicatorCache(roc, System.currentTimeMillis()));
        return roc;
    }

    /**
     * 计算资金流量指数（Money Flow Index）
     *
     * @param highs   最高价列表
     * @param lows    最低价列表
     * @param closes  收盘价列表
     * @param volumes 成交量列表
     * @param period  周期
     * @return MFI值
     */
    public static double calculateMFI(List<BigDecimal> highs, List<BigDecimal> lows,
                                      List<BigDecimal> closes, List<BigDecimal> volumes, int period) {
        if (highs == null || lows == null || closes == null || volumes == null ||
            highs.size() < period + 1 || lows.size() < period + 1 ||
            closes.size() < period + 1 || volumes.size() < period + 1) {
            return 50.0; // 默认中性值
        }

        String cacheKey = generateCacheKey("MFI", closes, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        double positiveMoneyFlow = 0.0;
        double negativeMoneyFlow = 0.0;

        for (int i = 0; i < period; i++) {
            // 计算典型价格
            double currentTP = (highs.get(i).doubleValue() + lows.get(i).doubleValue() + closes.get(i).doubleValue()) / 3.0;
            double prevTP = (highs.get(i + 1).doubleValue() + lows.get(i + 1).doubleValue() + closes.get(i + 1).doubleValue()) / 3.0;

            // 计算资金流量
            double moneyFlow = currentTP * volumes.get(i).doubleValue();

            if (currentTP > prevTP) {
                positiveMoneyFlow += moneyFlow;
            } else if (currentTP < prevTP) {
                negativeMoneyFlow += moneyFlow;
            }
        }

        double mfi = 50.0; // 默认值
        if (negativeMoneyFlow != 0) {
            double moneyFlowRatio = positiveMoneyFlow / negativeMoneyFlow;
            mfi = 100.0 - (100.0 / (1.0 + moneyFlowRatio));
        }

        cache.put(cacheKey, new IndicatorCache(mfi, System.currentTimeMillis()));
        return mfi;
    }

    /**
     * 计算抛物线SAR指标
     *
     * @param highs              最高价列表
     * @param lows               最低价列表
     * @param closes             收盘价列表
     * @param accelerationFactor 加速因子
     * @param maxAcceleration    最大加速因子
     * @return SAR值
     */
    public static double calculateParabolicSAR(List<BigDecimal> highs, List<BigDecimal> lows,
                                               List<BigDecimal> closes, double accelerationFactor, double maxAcceleration) {
        if (highs == null || lows == null || closes == null || highs.size() < 2) {
            return 0.0;
        }

        String cacheKey = generateCacheKey("SAR", closes, (int) (accelerationFactor * 100), (int) (maxAcceleration * 100));
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        // 简化的SAR计算
        double currentHigh = highs.get(0).doubleValue();
        double currentLow = lows.get(0).doubleValue();
        double prevHigh = highs.get(1).doubleValue();
        double prevLow = lows.get(1).doubleValue();

        // 判断趋势方向
        boolean isUptrend = currentHigh > prevHigh;

        double sar;
        if (isUptrend) {
            sar = Math.min(currentLow, prevLow) * (1 - accelerationFactor);
        } else {
            sar = Math.max(currentHigh, prevHigh) * (1 + accelerationFactor);
        }

        cache.put(cacheKey, new IndicatorCache(sar, System.currentTimeMillis()));
        return sar;
    }

    /**
     * 计算标准差
     *
     * @param prices 价格列表
     * @param period 周期
     * @return 标准差值
     */
    public static double calculateStandardDeviation(List<BigDecimal> prices, int period) {
        if (prices == null || prices.size() < period) {
            return 0.0;
        }

        String cacheKey = generateCacheKey("STDDEV", prices, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        // 计算平均值
        double mean = calculateSMA(prices, period);

        // 计算方差
        double variance = 0.0;
        for (int i = 0; i < period; i++) {
            double diff = prices.get(i).doubleValue() - mean;
            variance += diff * diff;
        }
        variance /= period;

        // 计算标准差
        double stdDev = Math.sqrt(variance);

        cache.put(cacheKey, new IndicatorCache(stdDev, System.currentTimeMillis()));
        return stdDev;
    }

    /**
     * 计算价格通道（Donchian Channel）
     *
     * @param highs  最高价列表
     * @param lows   最低价列表
     * @param period 周期
     * @return 通道数组 [上轨, 中轨, 下轨]
     */
    public static double[] calculateDonchianChannel(List<BigDecimal> highs, List<BigDecimal> lows, int period) {
        if (highs == null || lows == null || highs.size() < period || lows.size() < period) {
            return new double[]{0.0, 0.0, 0.0};
        }

        String cacheKey = generateCacheKey("DC", highs, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return (double[]) cached.getRawValue();
        }

        // 找到周期内的最高价和最低价
        double highestHigh = Double.MIN_VALUE;
        double lowestLow = Double.MAX_VALUE;

        for (int i = 0; i < period; i++) {
            highestHigh = Math.max(highestHigh, highs.get(i).doubleValue());
            lowestLow = Math.min(lowestLow, lows.get(i).doubleValue());
        }

        // 计算中轨（上下轨的平均值）
        double middleLine = (highestHigh + lowestLow) / 2.0;

        double[] result = {highestHigh, middleLine, lowestLow};
        cache.put(cacheKey, new IndicatorCache(0.0, System.currentTimeMillis(), result));
        return result;
    }

    /**
     * 计算成交量加权平均价格（VWAP）
     *
     * @param highs   最高价列表
     * @param lows    最低价列表
     * @param closes  收盘价列表
     * @param volumes 成交量列表
     * @param period  周期
     * @return VWAP值
     */
    public static double calculateVWAP(List<BigDecimal> highs, List<BigDecimal> lows,
                                       List<BigDecimal> closes, List<BigDecimal> volumes, int period) {
        if (highs == null || lows == null || closes == null || volumes == null ||
            highs.size() < period || lows.size() < period ||
            closes.size() < period || volumes.size() < period) {
            return 0.0;
        }

        String cacheKey = generateCacheKey("VWAP", closes, period);
        IndicatorCache cached = cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            return cached.getValue();
        }

        double totalPriceVolume = 0.0;
        double totalVolume = 0.0;

        for (int i = 0; i < period; i++) {
            // 计算典型价格
            double typicalPrice = (highs.get(i).doubleValue() + lows.get(i).doubleValue() + closes.get(i).doubleValue()) / 3.0;
            double volume = volumes.get(i).doubleValue();

            totalPriceVolume += typicalPrice * volume;
            totalVolume += volume;
        }

        double vwap = 0.0;
        if (totalVolume != 0) {
            vwap = totalPriceVolume / totalVolume;
        }

        cache.put(cacheKey, new IndicatorCache(vwap, System.currentTimeMillis()));
        return vwap;
    }

    /**
     * 获取指标统计信息
     *
     * @return 指标统计信息
     */
    public static String getIndicatorStatistics() {
        return String.format("技术指标库统计: 缓存条目=%d, 支持指标=17+", cache.size());
    }

    /**
     * 生成缓存键
     */
    private static String generateCacheKey(String indicator, List<BigDecimal> prices, int... params) {
        StringBuilder key = new StringBuilder(indicator);
        key.append("_").append(prices.size());
        if (prices.size() > 0) {
            key.append("_").append(prices.get(0).toString());
        }
        for (int param : params) {
            key.append("_").append(param);
        }
        return key.toString();
    }

    /**
     * 清理过期缓存
     */
    public static void cleanExpiredCache() {
        long currentTime = System.currentTimeMillis();
        cache.entrySet().removeIf(entry -> entry.getValue().isExpired(currentTime));
    }

    /**
     * 增量计算EMA（性能优化版本）
     *
     * @param previousEMA 前一个EMA值
     * @param currentPrice 当前价格
     * @param period 周期
     * @return 新的EMA值
     */
    public static double calculateEMAIncremental(double previousEMA, double currentPrice, int period) {
        double multiplier = 2.0 / (period + 1);
        return (currentPrice * multiplier) + (previousEMA * (1 - multiplier));
    }

    /**
     * 增量计算SMA（性能优化版本）
     *
     * @param previousSMA 前一个SMA值
     * @param newPrice 新价格
     * @param oldPrice 被移除的旧价格
     * @param period 周期
     * @return 新的SMA值
     */
    public static double calculateSMAIncremental(double previousSMA, double newPrice, double oldPrice, int period) {
        return previousSMA + (newPrice - oldPrice) / period;
    }

    /**
     * 增量计算RSI（性能优化版本）
     *
     * @param previousRSI 前一个RSI值
     * @param currentPrice 当前价格
     * @param previousPrice 前一个价格
     * @param previousAvgGain 前一个平均收益
     * @param previousAvgLoss 前一个平均损失
     * @param period 周期
     * @return RSI计算结果数组 [RSI值, 新平均收益, 新平均损失]
     */
    public static double[] calculateRSIIncremental(double previousRSI, double currentPrice, double previousPrice,
                                                 double previousAvgGain, double previousAvgLoss, int period) {
        double change = currentPrice - previousPrice;
        double gain = Math.max(change, 0);
        double loss = Math.max(-change, 0);

        // 计算新的平均收益和损失
        double newAvgGain = ((previousAvgGain * (period - 1)) + gain) / period;
        double newAvgLoss = ((previousAvgLoss * (period - 1)) + loss) / period;

        // 计算新的RSI
        double newRSI = 50.0;
        if (newAvgLoss != 0) {
            double rs = newAvgGain / newAvgLoss;
            newRSI = 100.0 - (100.0 / (1.0 + rs));
        }

        return new double[]{newRSI, newAvgGain, newAvgLoss};
    }

    /**
     * 批量计算多个指标（性能优化）
     *
     * @param highs 最高价列表
     * @param lows 最低价列表
     * @param closes 收盘价列表
     * @param volumes 成交量列表（可选）
     * @param config 指标配置
     * @return 指标结果映射
     */
    public static Map<String, Object> calculateMultipleIndicators(List<BigDecimal> highs, List<BigDecimal> lows,
                                                                List<BigDecimal> closes, List<BigDecimal> volumes,
                                                                IndicatorConfig config) {
        Map<String, Object> results = new ConcurrentHashMap<>();

        if (closes == null || closes.isEmpty()) {
            return results;
        }

        // 并行计算多个指标
        if (config.isCalculateEMA()) {
            results.put("EMA_FAST", calculateEMA(closes, config.getEmaFastPeriod()));
            results.put("EMA_MID", calculateEMA(closes, config.getEmaMidPeriod()));
            results.put("EMA_SLOW", calculateEMA(closes, config.getEmaSlowPeriod()));
        }

        if (config.isCalculateRSI()) {
            results.put("RSI", calculateRSI(closes, config.getRsiPeriod()));
        }

        if (config.isCalculateMACD()) {
            double[] macd = calculateMACD(closes, config.getMacdFast(), config.getMacdSlow(), config.getMacdSignal());
            results.put("MACD_LINE", macd[0]);
            results.put("MACD_SIGNAL", macd[1]);
            results.put("MACD_HISTOGRAM", macd[2]);
        }

        if (config.isCalculateATR() && highs != null && lows != null) {
            results.put("ATR", calculateATR(highs, lows, closes, config.getAtrPeriod()));
        }

        if (config.isCalculateBollingerBands()) {
            double[] bb = calculateBollingerBands(closes, config.getBbPeriod(), config.getBbStdDev());
            results.put("BB_UPPER", bb[0]);
            results.put("BB_MIDDLE", bb[1]);
            results.put("BB_LOWER", bb[2]);
        }

        return results;
    }

    /**
     * 指标配置类
     */
    public static class IndicatorConfig {
        private boolean calculateEMA = true;
        private boolean calculateRSI = true;
        private boolean calculateMACD = true;
        private boolean calculateATR = true;
        private boolean calculateBollingerBands = true;

        private int emaFastPeriod = 9;
        private int emaMidPeriod = 21;
        private int emaSlowPeriod = 55;
        private int rsiPeriod = 14;
        private int macdFast = 12;
        private int macdSlow = 26;
        private int macdSignal = 9;
        private int atrPeriod = 14;
        private int bbPeriod = 20;
        private double bbStdDev = 2.0;

        // Getters and setters
        public boolean isCalculateEMA() { return calculateEMA; }
        public void setCalculateEMA(boolean calculateEMA) { this.calculateEMA = calculateEMA; }

        public boolean isCalculateRSI() { return calculateRSI; }
        public void setCalculateRSI(boolean calculateRSI) { this.calculateRSI = calculateRSI; }

        public boolean isCalculateMACD() { return calculateMACD; }
        public void setCalculateMACD(boolean calculateMACD) { this.calculateMACD = calculateMACD; }

        public boolean isCalculateATR() { return calculateATR; }
        public void setCalculateATR(boolean calculateATR) { this.calculateATR = calculateATR; }

        public boolean isCalculateBollingerBands() { return calculateBollingerBands; }
        public void setCalculateBollingerBands(boolean calculateBollingerBands) { this.calculateBollingerBands = calculateBollingerBands; }

        public int getEmaFastPeriod() { return emaFastPeriod; }
        public void setEmaFastPeriod(int emaFastPeriod) { this.emaFastPeriod = emaFastPeriod; }

        public int getEmaMidPeriod() { return emaMidPeriod; }
        public void setEmaMidPeriod(int emaMidPeriod) { this.emaMidPeriod = emaMidPeriod; }

        public int getEmaSlowPeriod() { return emaSlowPeriod; }
        public void setEmaSlowPeriod(int emaSlowPeriod) { this.emaSlowPeriod = emaSlowPeriod; }

        public int getRsiPeriod() { return rsiPeriod; }
        public void setRsiPeriod(int rsiPeriod) { this.rsiPeriod = rsiPeriod; }

        public int getMacdFast() { return macdFast; }
        public void setMacdFast(int macdFast) { this.macdFast = macdFast; }

        public int getMacdSlow() { return macdSlow; }
        public void setMacdSlow(int macdSlow) { this.macdSlow = macdSlow; }

        public int getMacdSignal() { return macdSignal; }
        public void setMacdSignal(int macdSignal) { this.macdSignal = macdSignal; }

        public int getAtrPeriod() { return atrPeriod; }
        public void setAtrPeriod(int atrPeriod) { this.atrPeriod = atrPeriod; }

        public int getBbPeriod() { return bbPeriod; }
        public void setBbPeriod(int bbPeriod) { this.bbPeriod = bbPeriod; }

        public double getBbStdDev() { return bbStdDev; }
        public void setBbStdDev(double bbStdDev) { this.bbStdDev = bbStdDev; }
    }

    /**
     * 指标缓存类
     */
    private static class IndicatorCache {
        private final double value;
        private final long timestamp;
        private final Object rawValue;

        public IndicatorCache(double value, long timestamp) {
            this(value, timestamp, null);
        }

        public IndicatorCache(double value, long timestamp, Object rawValue) {
            this.value = value;
            this.timestamp = timestamp;
            this.rawValue = rawValue;
        }

        public double getValue() {
            return value;
        }

        public Object getRawValue() {
            return rawValue != null ? rawValue : value;
        }

        public boolean isExpired() {
            return isExpired(System.currentTimeMillis());
        }

        public boolean isExpired(long currentTime) {
            return (currentTime - timestamp) > CACHE_EXPIRE_TIME;
        }
    }
}
