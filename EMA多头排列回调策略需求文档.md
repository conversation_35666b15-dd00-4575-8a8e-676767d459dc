# EMA多头排列回调策略需求文档

## 1. 策略概述

### 1.1 策略名称
EMA多头排列回调策略

### 1.2 策略简介
该策略基于指数移动平均线(EMA)的多头排列形态，在价格回调至指定区域时，通过小周期确认后入场做多。策略遵循趋势跟随原则，同时利用价格回调提供更优的入场点位，提高风险收益比。

### 1.3 策略逻辑
- 主要判断条件：EMA9 > EMA21 > EMA55（均线多头排列，确认上升趋势）
- 回调区域判断：当前价格回调至EMA21与EMA55之间距离的80%处
- 入场确认：通过小周期K线形态确认买入信号

## 2. 技术指标及参数

### 2.1 核心指标
- EMA9: 9周期指数移动平均线
- EMA21: 21周期指数移动平均线
- EMA55: 55周期指数移动平均线

### 2.2 参数设置
- 主周期：4小时K线（判断趋势）
- 次周期：5分钟K线（确认入场）
- 回调深度比例：80%（可配置，范围60%-90%）
- 止损比例：ema55下方1.5倍ATR
- 止盈比例：10倍ATR平仓50%  25倍ATR平掉全部仓位

## 3. 策略实现流程

### 3.1 市场筛选
1. 筛选交易量适中、流动性良好的交易对

### 3.2 趋势判断
1. 在4小时周期计算EMA9、EMA21和EMA55
2. 判断是否满足EMA9 > EMA21 > EMA55条件
3. 计算EMA指标间距离和斜率，确认趋势强度

### 3.3 回调区域计算
1. 计算EMA21与EMA55之间的距离：distance = EMA55 - EMA21
2. 计算目标回调价格：targetPrice = EMA21 + distance * 0.8
3. 判断当前价格是否低于targetPrice

### 3.4 入场信号确认
1. 当4小时周期满足条件后，切换到5分钟周期
2. 在5分钟周期寻找支撑形态，如：
   - 5分钟RSI超卖回升
   - K线形成看涨形态（锤子线、启明星等）
   - 成交量放大且收阳
3. 满足5分钟周期确认后，执行入场操作



## 4. 风险管理

### 4.1 止损策略
- 固定止损：ema55下方1.5倍ATR
